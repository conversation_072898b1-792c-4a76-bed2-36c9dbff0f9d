import { api } from './api';
import { WorkSession } from '../../types/factory';

export const workSessionService = {
  // Get all work sessions
  getAllWorkSessions: async (): Promise<WorkSession[]> => {
    const response = await api.get('/work-sessions');
    return response.data;
  },

  // Get a single work session by ID
  getWorkSession: async (id: string): Promise<WorkSession> => {
    const response = await api.get(`/work-sessions/${id}`);
    return response.data;
  },

  // Get work sessions by worker ID
  getWorkSessionsByWorker: async (workerId: string): Promise<WorkSession[]> => {
    const response = await api.get(`/work-sessions/worker/${workerId}`);
    return response.data;
  },

  // Get work sessions by factory ID
  getWorkSessionsByFactory: async (factoryId: string): Promise<WorkSession[]> => {
    const response = await api.get(`/work-sessions/factory/${factoryId}`);
    return response.data;
  }
}; 