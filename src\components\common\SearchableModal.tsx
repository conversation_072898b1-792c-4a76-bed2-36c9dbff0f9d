import React, { useState, useMemo } from 'react';
import { X, Search } from 'lucide-react';
import { SearchableModalProps, SearchableItem } from '../../types/searchableModal';

/**
 * SearchableModal Component
 * 
 * A reusable modal component that displays a list of items with search functionality
 */
const SearchableModal: React.FC<SearchableModalProps> = ({
  isOpen,
  onClose,
  title,
  icon: Icon,
  data = [],
  loading = false,
  onItemClick,
  renderItem,
  searchPlaceholder = "Search...",
  searchFilter
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Default search filter function
  const defaultSearchFilter = (items: SearchableItem[], term: string): SearchableItem[] => {
    if (!term.trim()) return items;
    
    const searchLower = term.toLowerCase();
    return items.filter(item => {
      // Search through common fields
      const searchableFields = [
        item.name,
        item.username,
        item.title,
        (item as any).state?.name,
        (item as any).region?.name,
        item.location,
        item.description
      ];
      
      return searchableFields.some(field => 
        field && field.toString().toLowerCase().includes(searchLower)
      );
    });
  };

  // Filter data based on search term
  const filteredData = useMemo(() => {
    const filterFunction = searchFilter || defaultSearchFilter;
    return filterFunction(data, searchTerm);
  }, [data, searchTerm, searchFilter]);

  // Handle item click
  const handleItemClick = (item: SearchableItem) => {
    if (onItemClick) {
      onItemClick(item);
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Clear search when modal closes
  const handleClose = () => {
    setSearchTerm('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-2xl font-bold text-white flex items-center gap-3">
            {Icon && <Icon className="w-7 h-7 text-neonBlue" />}
            {title}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Search Bar */}
        <div className="p-6 border-b border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neonBlue focus:border-transparent transition-all"
            />
          </div>
          {searchTerm && (
            <div className="mt-2 text-sm text-gray-400">
              {filteredData.length} result{filteredData.length !== 1 ? 's' : ''} found
            </div>
          )}
        </div>

        {/* Modal Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-neonBlue text-lg">Loading...</div>
            </div>
          ) : filteredData.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredData.map((item, index) => (
                <div
                  key={item.id || index}
                  className="bg-gray-800 rounded-lg p-4 hover:bg-gray-750 transition-all duration-200 cursor-pointer border-2 border-transparent hover:border-neonBlue/30 group"
                  onClick={() => handleItemClick(item)}
                >
                  {renderItem ? renderItem(item) : (
                    <div>
                      <h3 className="text-lg font-semibold text-white">
                        {item.name || item.username || item.title || 'Unknown'}
                      </h3>
                      <p className="text-gray-400 text-sm mt-1">
                        {item.description || item.location || 'No description available'}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="text-gray-400 text-lg mb-2">
                {searchTerm ? 'No results found' : 'No data available'}
              </div>
              {searchTerm && (
                <div className="text-gray-500 text-sm">
                  Try adjusting your search terms
                </div>
              )}
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div className="px-6 py-4 border-t border-gray-700 bg-gray-850">
          <div className="flex justify-between items-center text-sm text-gray-400">
            <span>
              Showing {filteredData.length} of {data.length} items
              {searchTerm && ` for "${searchTerm}"`}
            </span>
            <button
              onClick={handleClose}
              className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchableModal;
