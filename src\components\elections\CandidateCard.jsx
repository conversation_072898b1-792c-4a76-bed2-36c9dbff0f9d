import React from 'react';
import { Link } from 'react-router-dom';
import { User, Users, Award, FileText } from 'lucide-react';
import { ELECTION_CONFIG } from '../../config/election.config';

const CandidateCard = ({
  candidate,
  totalVotes,
  isSelected,
  onSelect,
  hasUserVoted,
  showVoteButton = true,
  showResults = false
}) => {
  // Handle different backend data structures
  const candidateId = candidate.id || candidate.userId || candidate.user?.id;
  const candidateUsername = candidate.user?.username || candidate.username || 'Unknown';
  const candidateVotes = candidate.voteCount || candidate.votes || 0;
  const candidateParty = candidate.party || null;
  const candidateManifesto = candidate.manifesto || '';
  const candidateAvatar = candidate.user?.avatarUrl || candidate.avatarUrl || null;

  const votePercentage = totalVotes > 0 ? (candidateVotes / totalVotes) * 100 : 0;
  const isWinner = showResults && votePercentage > ELECTION_CONFIG.WINNER_THRESHOLD_PERCENTAGE;

  const handleCardClick = () => {
    if (!hasUserVoted && onSelect) {
      onSelect(candidateId);
    }
  };

  return (
    <div
      className={`
        relative bg-gray-800 rounded-lg border transition-all duration-200 cursor-pointer
        ${isSelected
          ? 'border-blue-500 bg-blue-900/20 shadow-lg shadow-blue-500/20'
          : 'border-gray-600 hover:border-gray-500'
        }
        ${hasUserVoted && !showVoteButton ? 'opacity-75' : ''}
        ${isWinner ? 'ring-2 ring-yellow-500' : ''}
      `}
      onClick={handleCardClick}
    >
      {/* Winner Badge */}
      {isWinner && (
        <div className="absolute -top-2 -right-2 bg-yellow-500 text-black px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
          <Award className="w-3 h-3" />
          <span>Winner</span>
        </div>
      )}

      <div className="p-4">
        {/* Candidate Header */}
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center">
            {candidateAvatar ? (
              <img
                src={candidateAvatar}
                alt={candidateUsername}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <User className="w-6 h-6 text-gray-400" />
            )}
          </div>
          <div className="flex-1">
            <Link
              to={`/users/${candidateId}`}
              className="text-white font-semibold hover:text-blue-400 transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              {candidateUsername}
            </Link>
            {candidateParty && (
              <p className="text-sm text-gray-400">
                {candidateParty.name}
              </p>
            )}
          </div>
        </div>

        {/* Vote Count and Percentage */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center space-x-2 text-gray-300">
              <Users className="w-4 h-4" />
              <span className="text-sm">Votes</span>
            </div>
            <div className="text-right">
              <span className="text-white font-semibold">{candidateVotes}</span>
              {showResults && (
                <span className="text-gray-400 text-sm ml-2">
                  ({votePercentage.toFixed(1)}%)
                </span>
              )}
            </div>
          </div>

          {/* Vote Progress Bar */}
          {showResults && (
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-500 ${
                  isWinner ? 'bg-yellow-500' : 'bg-blue-500'
                }`}
                style={{ width: `${votePercentage}%` }}
              ></div>
            </div>
          )}
        </div>

        {/* Manifesto Preview */}
        {candidateManifesto && (
          <div className="mb-3">
            <div className="flex items-center space-x-2 text-gray-400 mb-1">
              <FileText className="w-4 h-4" />
              <span className="text-xs">Manifesto</span>
            </div>
            <p className="text-sm text-gray-300 line-clamp-2">
              {candidateManifesto.length > ELECTION_CONFIG.MANIFESTO_PREVIEW_LENGTH
                ? `${candidateManifesto.substring(0, ELECTION_CONFIG.MANIFESTO_PREVIEW_LENGTH)}...`
                : candidateManifesto
              }
            </p>
          </div>
        )}

        {/* Selection Indicator */}
        {isSelected && (
          <div className="flex items-center space-x-2 text-blue-400 text-sm">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>Selected</span>
          </div>
        )}

        {/* Vote Button */}
        {showVoteButton && !hasUserVoted && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (onSelect) onSelect(candidateId);
            }}
            className={`
              w-full mt-3 py-2 px-4 rounded-md font-medium transition-colors
              ${isSelected
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              }
            `}
          >
            {isSelected ? 'Selected' : 'Select Candidate'}
          </button>
        )}

        {/* Already Voted Indicator */}
        {hasUserVoted && !showVoteButton && (
          <div className="w-full mt-3 py-2 px-4 rounded-md bg-gray-700 text-gray-400 text-center text-sm">
            You have already voted
          </div>
        )}
      </div>
    </div>
  );
};

export default CandidateCard;
