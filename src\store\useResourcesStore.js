import { create } from "zustand";
import useUserDataStore from './useUserDataStore';

const useResourcesStore = create((set) => ({
  money: 0,
  gold: 0,
  loading: false,

  fetchResources: async () => {
    set({ loading: true });

    // Use the user data store to get the data
    const userDataStore = useUserDataStore.getState();
    const userData = userDataStore.userData;

    // If we already have user data, use it
    if (userData) {
      const { money, gold } = userData;
      set({ money, gold, loading: false });
      return;
    }

    set({ loading: false });
  }
}));

export default useResourcesStore;
