import { api } from "./api";

export interface CreatePartyDto {
  name: string;
  description?: string;
  regionId: string;
}
export interface UpdatePartyDto {
  id: number;
  name: string;
  description?: string;
}
export interface Party {
  id: string;
  name: string;
  description: string;
  leader: {
    id: number;
    username: string;
  };
  region: {
    id: string;
    name: string;
  };
  members: Array<{
    id: number;
    username: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface CreateJoinRequestDto {
  partyId: string;
}

export interface JoinRequestResponseDto {
  id: string;
  status: "pending" | "accepted" | "rejected";
  user: {
    id: number;
    username: string;
  };
  party: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

export const partyService = {
  createParty: async (createPartyDto: CreatePartyDto): Promise<Party> => {
    const response = await api.post("/party", createPartyDto);
    return response.data;
  },

  getPartyById: async (id: string): Promise<Party> => {
    const response = await api.get(`/party/${id}`);
    return response.data;
  },

  getPartiesByRegion: async (regionId: string): Promise<Party[]> => {
    const response = await api.get(`/party/region/${regionId}`);
    return response.data;
  },

  joinParty: async (partyId: string): Promise<Party> => {
    const response = await api.post(`/party/${partyId}/join`);
    return response.data;
  },

  leaveParty: async (partyId: string): Promise<Party> => {
    const response = await api.post(`/party/${partyId}/leave`);
    return response.data;
  },

  updateParty: async (partyData: UpdatePartyDto): Promise<Party> => {
    const response = await api.patch(`/party/${partyData.id}`, partyData);
    return response.data;
  },

  // Party Join Request endpoints
  createJoinRequest: async (
    partyId: string
  ): Promise<JoinRequestResponseDto> => {
    const response = await api.post("/party-requests", { partyId });
    return response.data;
  },

  getUserJoinRequests: async (): Promise<JoinRequestResponseDto[]> => {
    const response = await api.get("/party-requests/user");
    return response.data;
  },

  getPartyJoinRequests: async (
    partyId: string
  ): Promise<JoinRequestResponseDto[]> => {
    const response = await api.get(`/party-requests/party/${partyId}`);
    return response.data;
  },

  acceptJoinRequest: async (requestId: string): Promise<Party> => {
    const response = await api.patch(`/party-requests/${requestId}/accept`);
    return response.data;
  },

  rejectJoinRequest: async (
    requestId: string
  ): Promise<JoinRequestResponseDto> => {
    const response = await api.patch(`/party-requests/${requestId}/reject`);
    return response.data;
  },

  cancelJoinRequest: async (
    requestId: string
  ): Promise<JoinRequestResponseDto> => {
    const response = await api.delete(`/party-requests/${requestId}`);
    return response.data;
  },

  // Transfer party leadership to another member
  transferLeadership: async (
    partyId: string,
    newLeaderId: number
  ): Promise<Party> => {
    const response = await api.post(
      `/party/${partyId}/transfer-leadership/${newLeaderId}`
    );
    return response.data;
  },

  // Kick a member from the party (leader only)
  kickMember: async (partyId: string, memberId: number): Promise<Party> => {
    const response = await api.delete(`/party/${partyId}/kick/${memberId}`);
    return response.data;
  },
};
