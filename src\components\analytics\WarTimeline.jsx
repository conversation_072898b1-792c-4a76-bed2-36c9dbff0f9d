import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';

const WarTimeline = ({ limit = 10 }) => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTimeline = async () => {
      try {
        setLoading(true);
        const data = await warService.getWarTimeline(limit);
        setEvents(data);
      } catch (error) {
        console.error('Failed to fetch war timeline:', error);
        toast.error('Failed to load war timeline');
      } finally {
        setLoading(false);
      }
    };

    fetchTimeline();
  }, [limit]);

  const getEventIcon = (eventType) => {
    switch (eventType.toLowerCase()) {
      case 'declaration':
        return '📜';
      case 'battle':
        return '⚔️';
      case 'victory':
        return '🏆';
      case 'defeat':
        return '❌';
      case 'peace':
        return '🕊️';
      case 'join':
        return '➕';
      case 'supply':
        return '📦';
      default:
        return '🔔';
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6 animate-pulse">
        <div className="h-6 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-700 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (events.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">War Timeline</h2>
        <p className="text-gray-400">No recent war events</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">War Timeline</h2>
      
      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-600"></div>
        
        {/* Timeline events */}
        <div className="space-y-6">
          {events.map((event, index) => (
            <div key={index} className="relative pl-14">
              {/* Event icon */}
              <div className="absolute left-0 w-12 h-12 flex items-center justify-center bg-gray-700 rounded-full border-4 border-gray-800 z-10">
                <span className="text-xl">{getEventIcon(event.eventType)}</span>
              </div>
              
              {/* Event content */}
              <div className="bg-gray-700 p-4 rounded-md">
                <div className="flex justify-between items-start mb-2">
                  <Link 
                    to={`/wars/${event.warId}`} 
                    className="text-lg font-medium text-neonBlue hover:text-blue-400"
                  >
                    {event.warName}
                  </Link>
                  <span className="text-xs text-gray-400">
                    {new Date(event.timestamp).toLocaleString()}
                  </span>
                </div>
                <p className="text-gray-300">{event.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {events.length >= limit && (
        <div className="mt-6 text-center">
          <Link to="/wars" className="text-neonBlue hover:text-blue-400">
            View more war events
          </Link>
        </div>
      )}
    </div>
  );
};

export default WarTimeline;
