import React, { useState, useEffect } from "react";
import { travelService } from "../../services/api/travel.service";
import { FaClock, FaShip, FaFlag } from "react-icons/fa";
import useUserDataStore from "../../store/useUserDataStore";

const TravelTimeEstimator = ({ destinationRegionId }) => {
  const [estimate, setEstimate] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { userData } = useUserDataStore();

  useEffect(() => {
    const fetchEstimate = async () => {
      if (!userData?.region?.id || !destinationRegionId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const result = await travelService.getTravelTimeEstimate({
          originRegionId: userData.region.id,
          destinationRegionId
        });
        
        setEstimate(result);
      } catch (err) {
        console.error("Error fetching travel time estimate:", err);
        setError("Failed to calculate travel time");
      } finally {
        setLoading(false);
      }
    };

    fetchEstimate();
  }, [userData?.region?.id, destinationRegionId]);

  if (loading) {
    return (
      <div className="bg-gray-700/30 rounded-lg p-3 animate-pulse">
        <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-600 rounded w-1/2"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 text-red-400 rounded-lg p-3 text-sm">
        {error}
      </div>
    );
  }

  if (!estimate) {
    return null;
  }

  // Format travel time in hours and minutes
  const formatTravelTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours === 0) {
      return `${mins} minutes`;
    } else if (mins === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${hours} hour${hours > 1 ? 's' : ''} ${mins} minute${mins > 1 ? 's' : ''}`;
    }
  };

  return (
    <div className="bg-gray-700/30 rounded-lg p-3 text-sm space-y-2">
      <div className="flex items-center">
        <FaClock className="text-blue-400 mr-2" />
        <span className="text-gray-300">
          Travel time: <span className="text-white font-medium">{formatTravelTime(estimate.travelTime)}</span>
        </span>
      </div>
      
      <div className="flex items-center">
        <span className="text-gray-300 flex items-center">
          {estimate.seaCrossing ? (
            <>
              <FaShip className="text-blue-400 mr-2" />
              Includes sea crossing
            </>
          ) : (
            <>
              <FaFlag className={`${estimate.sameState ? 'text-green-400' : 'text-yellow-400'} mr-2`} />
              {estimate.sameState ? 'Same state' : 'Different state'}
            </>
          )}
        </span>
      </div>
      
      {estimate.distance > 0 && (
        <div className="text-gray-300">
          Distance: <span className="text-white font-medium">{estimate.distance.toFixed(1)} km</span>
        </div>
      )}
    </div>
  );
};

export default TravelTimeEstimator;
