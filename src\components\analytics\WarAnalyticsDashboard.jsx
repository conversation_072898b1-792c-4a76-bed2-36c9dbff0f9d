import React, { useState } from 'react';
import GlobalWarStats from './GlobalWarStats';
import UserWarStats from './UserWarStats.tsx';
import WarTimeline from './WarTimeline';
import DamageLeaderboard from './DamageLeaderboard';
import EfficiencyMetrics from './EfficiencyMetrics';
import WarTrends from './WarTrends';

const WarAnalyticsDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg shadow-lg">
        <div className="border-b border-gray-700">
          <nav className="flex overflow-x-auto">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-4 px-6 font-medium ${
                activeTab === 'overview'
                  ? 'text-neonBlue border-b-2 border-neonBlue'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('personal')}
              className={`py-4 px-6 font-medium ${
                activeTab === 'personal'
                  ? 'text-neonBlue border-b-2 border-neonBlue'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              Personal Stats
            </button>
            <button
              onClick={() => setActiveTab('timeline')}
              className={`py-4 px-6 font-medium ${
                activeTab === 'timeline'
                  ? 'text-neonBlue border-b-2 border-neonBlue'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              Timeline
            </button>
            <button
              onClick={() => setActiveTab('leaderboard')}
              className={`py-4 px-6 font-medium ${
                activeTab === 'leaderboard'
                  ? 'text-neonBlue border-b-2 border-neonBlue'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              Leaderboard
            </button>
            <button
              onClick={() => setActiveTab('efficiency')}
              className={`py-4 px-6 font-medium ${
                activeTab === 'efficiency'
                  ? 'text-neonBlue border-b-2 border-neonBlue'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              Efficiency
            </button>
            <button
              onClick={() => setActiveTab('trends')}
              className={`py-4 px-6 font-medium ${
                activeTab === 'trends'
                  ? 'text-neonBlue border-b-2 border-neonBlue'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              Trends
            </button>
          </nav>
        </div>
      </div>

      {activeTab === 'overview' && <GlobalWarStats />}
      {activeTab === 'personal' && <UserWarStats />}
      {activeTab === 'timeline' && <WarTimeline limit={20} />}
      {activeTab === 'leaderboard' && <DamageLeaderboard limit={10} />}
      {activeTab === 'efficiency' && <EfficiencyMetrics limit={10} />}
      {activeTab === 'trends' && <WarTrends />}
    </div>
  );
};

export default WarAnalyticsDashboard;
