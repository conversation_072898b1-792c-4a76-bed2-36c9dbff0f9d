import React from 'react';
import useAuthStore from '../../store/useAuthStore';

const StateForm = ({
  formData,
  onChange,
  onSubmit,
  loading,
  onCancel,
  showRequirements = false,
  className = ""
}) => {
  const { user } = useAuthStore();

  return (
    <form onSubmit={onSubmit} className={`${className} text-gray-300`}>
      {showRequirements && (
        <div className="mb-6 p-4 bg-[#1a1f2e] rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-2">Requirements:</h3>
          <ul className="list-disc list-inside text-gray-300">
            <li>Cost: 500 gold</li>
          </ul>
        </div>
      )}

      <div className="mb-6">
        <label className="block text-gray-300 text-sm mb-2" htmlFor="name">
          State Name *
        </label>
        <input
          id="name"
          name="name"
          type="text"
          value={formData.name}
          onChange={onChange}
          className="w-full p-3 rounded bg-[#1a1f2e] text-gray-300 border-none focus:ring-1 focus:ring-blue-500 placeholder-gray-500"
          placeholder="Enter the name of your state"
          required
        />
      </div>

      <div className="mb-6">
        <label className="block text-gray-300 text-sm mb-2" htmlFor="regionId">
          Region
        </label>
        <input
          type="text"
          id="regionId"
          name="regionId"
          value={user?.region?.name || 'No region selected'}
          disabled
          className="w-full p-3 rounded bg-[#1a1f2e] text-gray-300 border-none placeholder-gray-500 cursor-not-allowed"
        />
      </div>
      <div className="mb-8">
        <label className="flex items-center">
          <input
            type="checkbox"
            name="includeLeaderRegion"
            checked={formData.includeLeaderRegion}
            onChange={onChange}
            className="mr-2 bg-[#1a1f2e] border-gray-600 rounded text-blue-500 focus:ring-blue-500"
          />
          <span className="text-gray-300 text-sm">Include my current region in the state</span>
        </label>
        <p className="text-xs text-gray-500 mt-1 ml-5">
          Your current region will be added to the state, and you will become the state leader
        </p>
      </div>

      <div className="flex items-center justify-between">
        <button
          type="button"
          onClick={onCancel}
          className="bg-[#1a1f2e] hover:bg-[#252b3b] text-gray-300 font-medium py-2 px-4 rounded"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className={`bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {loading ? 'Creating...' : 'Create State'}
        </button>
      </div>

      {(!user?.leadingParty && (!user?.parties || user?.parties.length === 0)) && (
        <div className="mt-4 text-center">
          <a href="/party/create" className="text-blue-500 hover:text-blue-400 text-sm">
            Create a Party First →
          </a>
        </div>
      )}
    </form>
  );
};

export default StateForm;
