import React, { useState } from 'react';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { stripeService } from '../../services/api/stripe.service';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { showErrorToast } from '../../utils/showErrorToast';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

const GoldPurchaseForm = ({ amount, onSuccess }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!stripe || !elements) return;

    setLoading(true);
    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        redirect: 'if_required'
      });

      if (error) {
        showErrorToast(error);
        return;
      }

      if (paymentIntent.status === 'succeeded') {
        showSuccessToast('Gold purchase successful!');
        onSuccess();
      }
    } catch (err) {
      showErrorToast(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <PaymentElement />
      <button
        type="submit"
        disabled={!stripe || loading}
        className="mt-4 w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded"
      >
        {loading ? 'Processing...' : 'Purchase Gold'}
      </button>
    </form>
  );
};

export default function GoldPurchaseModal({ isOpen, onClose, amount }) {
  const [clientSecret, setClientSecret] = useState(null);

  useEffect(() => {
    if (isOpen && amount) {
      stripeService.createGoldPurchaseIntent(amount)
        .then(({ clientSecret }) => setClientSecret(clientSecret))
        .catch(showErrorToast);
    }
  }, [isOpen, amount]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-gray-800 p-6 rounded-lg max-w-md w-full">
        <h2 className="text-xl font-bold text-white mb-4">Purchase Gold</h2>
        {clientSecret && (
          <Elements stripe={stripePromise} options={{ clientSecret }}>
            <GoldPurchaseForm amount={amount} onSuccess={onClose} />
          </Elements>
        )}
      </div>
    </div>
  );
}