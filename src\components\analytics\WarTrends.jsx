import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { toast } from 'react-toastify';

const WarTrends = () => {
  const [trends, setTrends] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTrends = async () => {
      try {
        setLoading(true);
        const data = await warService.getWarTrends();
        setTrends(data);
      } catch (error) {
        console.error('Failed to fetch war trends:', error);
        toast.error('Failed to load war trends');
      } finally {
        setLoading(false);
      }
    };

    fetchTrends();
  }, []);

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6 animate-pulse">
        <div className="h-6 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="h-64 bg-gray-700 rounded"></div>
      </div>
    );
  }

  if (trends.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">War Trends</h2>
        <p className="text-gray-400">No trend data available</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">War Trends</h2>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-600">
          <thead className="bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Timeframe
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Total Wars
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Attacker Win %
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Defender Win %
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Avg. Duration
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Avg. Participants
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">
                Most Common Type
              </th>
            </tr>
          </thead>
          <tbody className="bg-gray-800 divide-y divide-gray-700">
            {trends.map((trend, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-gray-750' : 'bg-gray-800'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                  {trend.timeframe}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {trend.totalWars}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                  <span className={trend.attackerWinRate > 0.5 ? 'text-green-400' : 'text-red-400'}>
                    {(trend.attackerWinRate * 100).toFixed(0)}%
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                  <span className={trend.defenderWinRate > 0.5 ? 'text-green-400' : 'text-red-400'}>
                    {(trend.defenderWinRate * 100).toFixed(0)}%
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {trend.averageDuration.toFixed(1)} days
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {trend.averageParticipants.toFixed(1)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-neonBlue">
                    {trend.mostCommonType}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Win Rate Trends</h3>
          <div className="h-48 flex items-end space-x-2">
            {trends.map((trend, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="w-full flex flex-col h-40">
                  <div 
                    className="w-full bg-red-500" 
                    style={{ height: `${trend.attackerWinRate * 100}%` }}
                  ></div>
                  <div 
                    className="w-full bg-blue-500" 
                    style={{ height: `${trend.defenderWinRate * 100}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-400 mt-2 transform -rotate-45 origin-top-left">
                  {trend.timeframe}
                </div>
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-6">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 mr-2"></div>
              <span className="text-xs text-gray-400">Attacker Win Rate</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 mr-2"></div>
              <span className="text-xs text-gray-400">Defender Win Rate</span>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">War Participation Trends</h3>
          <div className="h-48 flex items-end space-x-2">
            {trends.map((trend, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="w-full flex flex-col h-40 justify-end">
                  <div 
                    className="w-full bg-purple-500" 
                    style={{ height: `${(trend.averageParticipants / Math.max(...trends.map(t => t.averageParticipants))) * 100}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-400 mt-2 transform -rotate-45 origin-top-left">
                  {trend.timeframe}
                </div>
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-6">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-purple-500 mr-2"></div>
              <span className="text-xs text-gray-400">Average Participants</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarTrends;
