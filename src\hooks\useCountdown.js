import { useEffect, useState } from "react";

export function useCountdown(targetDate) {
  const [remainingTime, setRemainingTime] = useState(getTimeLeft());

  function getTimeLeft() {
    if (!targetDate) return null;

    const now = new Date().getTime();
    const target = new Date(targetDate).getTime();
    const diff = target - now;

    if (diff <= 0) return null;

    const minutes = Math.floor((diff / 1000 / 60) % 60);
    const hours = Math.floor(diff / 1000 / 60 / 60);

    return `${hours}h ${minutes}m`;
  }

  useEffect(() => {
    const interval = setInterval(() => {
      setRemainingTime(getTimeLeft());
    }, 60000);

    return () => clearInterval(interval);
  }, [targetDate]);

  return remainingTime;
}
