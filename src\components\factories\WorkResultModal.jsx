import React from 'react';

/**
 * Modal component to display work session results
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to close the modal
 * @param {Object} props.workSession - The work session data
 * @param {string} props.workSession.wageEarned - Money earned from work
 * @param {number} props.workSession.resourceEarned - Resources earned from work
 * @param {string} props.workSession.resourceType - Type of resource earned (GOLD, MONEY)
 * @param {number} props.workSession.energySpent - Energy spent on work
 * @param {number} props.workSession.efficiencyMultiplier - Efficiency multiplier for the work
 * @returns {JSX.Element|null} The modal component or null if not open
 */
const WorkResultModal = ({ isOpen, onClose, workSession }) => {
  if (!isOpen || !workSession) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl border border-gray-700">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-xl font-semibold text-white">Work Results</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="space-y-4">
          {/* Money earned */}
          <div className="bg-gray-700 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Money Earned:</span>
              <span className="text-green-400 font-semibold text-lg">
                +{workSession.wageEarned.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Resource earned */}
          <div className="bg-gray-700 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">{workSession.resourceType} Earned:</span>
              <span className={`font-semibold text-lg ${
                workSession.resourceType === 'GOLD' ? 'text-yellow-400' : 'text-green-400'
              }`}>
                +{workSession.resourceEarned.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Energy spent */}
          <div className="bg-gray-700 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Energy Spent:</span>
              <span className="text-blue-400 font-semibold">
                {workSession.energySpent}
              </span>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <button
            onClick={onClose}
            className="w-full bg-neonBlue hover:bg-blue-600 text-white py-2 px-4 rounded transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default WorkResultModal;
