export enum PreparationType {
  TROOPS = 'troops',
  SUPPLIES = 'supplies',
  FORTIFICATION = 'fortification'
}

export interface WarPreparationDto {
  type: PreparationType;
  amount: number;
  resources: {
    gold: number;
    ammunition: number;
    food: number;
    fuel: number;
  };
}

export interface WarPreparationResponse {
  id: string;
  preparationType: PreparationType;
  amount: number;
  bonusApplied: number;
  timestamp: string;
}

export interface WarSuppliesResponse {
  attackerSupplies: {
    ammunition: number;
    food: number;
    medical: number;
    fuel: number;
    supplyEfficiencyBonus: number;
  };
  defenderSupplies: {
    ammunition: number;
    food: number;
    medical: number;
    fuel: number;
    supplyEfficiencyBonus: number;
  };
}

export interface AddWarSuppliesDto {
  side: 'attacker' | 'defender';
  supplies: {
    ammunition?: number;
    food?: number;
    medical?: number;
    fuel?: number;
  };
}

export interface WarMoraleResponse {
  attackerMorale: number;
  defenderMorale: number;
  recentEvents: {
    type: string;
    moraleImpact: number;
    timestamp: string;
  }[];
}

export interface WarCostsResponse {
  attackerCosts: {
    gold: number;
    resources: {
      ammunition: number;
      fuel: number;
      food: number;
    };
  };
  defenderCosts: {
    gold: number;
    resources: {
      ammunition: number;
      fuel: number;
      food: number;
    };
  };
}

export interface BattleEventResponse {
  id: string;
  type: string;
  moraleImpact: number;
  eventDetails: any;
  timestamp: string;
}

export interface WarEventsResponse {
  events: BattleEventResponse[];
  pagination: {
    total: number;
    page: number;
    limit: number;
  };
}
