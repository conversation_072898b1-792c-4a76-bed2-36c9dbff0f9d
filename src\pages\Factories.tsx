import React, { useState, useEffect } from 'react';
import { factoryService } from '../services/api/factory.service';
import { Factory, WorkSession } from '../types/factory';
import { toast } from 'react-toastify';

const Factories: React.FC = () => {
  const [factories, setFactories] = useState<Factory[]>([]);
  const [workHistory, setWorkHistory] = useState<WorkSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [factoriesData, historyData] = await Promise.all([
        factoryService.getAllFactories(),
        factoryService.getWorkHistory()
      ]);
      setFactories(factoriesData);
      setWorkHistory(historyData);
    } catch (err) {
      setError('Failed to load factories and work history');
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleWork = async (factory: Factory) => {
    try {
      await factoryService.workAtFactory(factory.id, factory.energyCost);
      toast.success('Successfully worked at factory!');
      loadData(); // Reload data to update resources and history
    } catch (err) {
      toast.error('Failed to work at factory');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Factories</h1>

      {/* Factories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {factories.map((factory) => (
          <div
            key={factory.id}
            className="bg-gray-800 p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
          >
            <h2 className="text-xl font-semibold mb-2">{factory.name}</h2>
            <div className="space-y-2 text-gray-300">
              <p>Type: {factory.type}</p>
              <p>Region: {factory.region?.name || 'Unknown'}</p>
              <p>Owner: {factory.owner?.username || 'Unknown'}</p>
              <p>Wage: {factory.wage.toFixed(2)}</p>
              <p>Resource per work: {factory.resourcePerWork.toFixed(2)}</p>
              <p>Energy cost: {factory.energyCost}</p>
              <p>Workers: {factory.currentWorkers}/{factory.maxWorkers}</p>
            </div>
            <button
              onClick={() => handleWork(factory)}
              className="mt-4 w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded transition-colors"
            >
              Work at Factory
            </button>
          </div>
        ))}
      </div>

      {/* Work History */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Work History</h2>
        <div className="bg-gray-800 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-700">
                <th className="px-6 py-3 text-left">Factory</th>
                <th className="px-6 py-3 text-left">Energy Spent</th>
                <th className="px-6 py-3 text-left">Wage Earned</th>
                <th className="px-6 py-3 text-left">Resource Earned</th>
                <th className="px-6 py-3 text-left">Date</th>
              </tr>
            </thead>
            <tbody>
              {workHistory.map((session) => (
                <tr key={session.id} className="border-t border-gray-700">
                  <td className="px-6 py-4">{session.factory?.name}</td>
                  <td className="px-6 py-4">{session.energySpent}</td>
                  <td className="px-6 py-4">{session.wageEarned.toFixed(2)}</td>
                  <td className="px-6 py-4">{session.resourceEarned.toFixed(2)}</td>
                  <td className="px-6 py-4">
                    {new Date(session.createdAt).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Factories; 