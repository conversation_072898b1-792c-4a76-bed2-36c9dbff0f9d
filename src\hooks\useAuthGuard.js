import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import useAuthStore from "../store/useAuthStore";
import { isTokenValid } from "../utils/isTokenValid";

export function useAuthGuard() {
  const navigate = useNavigate();
  const { user, access_token, logout } = useAuthStore();

  useEffect(() => {
    const isValid = isTokenValid(access_token);
    if (!user || !isValid) {
      logout();
      navigate("/login");
    }
  }, [user, access_token, navigate, logout]);
}
