import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { stateService } from '../../services/api/state.service';
import CreateStateModal from './CreateStateModal';
import Navbar from '../Navbar';

const StatesList = () => {
  const [states, setStates] = useState([]);
  const [isCreateStateModalOpen, setIsCreateStateModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        setLoading(true);
        const data = await stateService.getAllStates();
        setStates(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching states:', err);
        setError('Failed to load states. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchStates();
  }, []);

  if (loading) return (
    <>
      <Navbar />
      <div className="flex justify-center items-center min-h-screen bg-gray-900">
        <div className="loader text-blue-500">Loading...</div>
      </div>
    </>
  );

  if (error) return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gray-900 p-4">
        <div className="text-red-500 bg-red-900/20 p-4 rounded-lg">{error}</div>
      </div>
    </>
  );

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gray-900 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-white">States</h1>
            <button 
              onClick={() => setIsCreateStateModalOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Create State
            </button>
          </div>

          {states.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">No states found.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {states.map((state) => (
                <div 
                  key={state.id} 
                  className="bg-gray-800 rounded-lg shadow-xl p-6 hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    {state.flagUrl && (
                      <img 
                        src={state.flagUrl} 
                        alt={`${state.name} flag`} 
                        className="w-16 h-16 object-cover rounded-lg border-2 border-gray-700"
                      />
                    )}
                    <div>
                      <h2 className="text-xl font-semibold text-white">{state.name}</h2>
                      <p className="text-gray-400">
                        Leader:{state?.leader?.username ? (
                          <Link className="p-2" to={`/users/${state?.leader?.id}`}>
                            <span className="text-blue-400">
                              {state.leader.username}
                            </span>
                          </Link>
                        ) : (
                          <span className="text-gray-400"> Unknown</span>
                        )}
                      </p>
                      <p className="text-gray-400">
                        Regions: {state.regions.length}
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-700">
                    <Link 
                      to={`/states/${state.id}`}
                      className="text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      View Details →
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}

          <CreateStateModal 
            isOpen={isCreateStateModalOpen}
            onClose={() => setIsCreateStateModalOpen(false)}
            onSuccess={(newState) => {
              setStates(prevStates => [...prevStates, newState]);
              setIsCreateStateModalOpen(false);
            }}
          />
        </div>
      </div>
    </>
  );
};

export default StatesList; 
