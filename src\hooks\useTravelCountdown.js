import { useState, useEffect } from 'react';

export function useTravelCountdown(estimatedArrivalTime) {
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [isCompleted, setIsCompleted] = useState(false);

  // Initial calculation function that doesn't modify state
  const calculateInitialTimeRemaining = () => {
    if (!estimatedArrivalTime) return null;

    const now = new Date().getTime();
    const arrivalTime = new Date(estimatedArrivalTime).getTime();
    const diff = arrivalTime - now;

    if (diff <= 0) {
      return { hours: 0, minutes: 0, seconds: 0, completed: true };
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return { hours, minutes, seconds, completed: false };
  };

  // Function to calculate time remaining that can update state
  function calculateTimeRemaining() {
    if (!estimatedArrivalTime) return null;

    const now = new Date().getTime();
    const arrivalTime = new Date(estimatedArrivalTime).getTime();
    const diff = arrivalTime - now;

    if (diff <= 0) {
      setIsCompleted(true);
      return { hours: 0, minutes: 0, seconds: 0 };
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return { hours, minutes, seconds };
  }

  function getFormattedTime() {
    if (!timeRemaining) return '';

    const { hours, minutes, seconds } = timeRemaining;

    if (hours === 0 && minutes === 0) {
      return `${seconds}s`;
    } else if (hours === 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${hours}h ${minutes}m ${seconds}s`;
    }
  }

  function getProgressPercentage() {
    if (!estimatedArrivalTime || !timeRemaining) return 0;

    // Calculate total duration (15 minutes = 900000 ms)
    const totalDuration = 900000; // Default to 15 minutes if we can't determine

    // Calculate elapsed time
    const remainingMs = (timeRemaining.hours * 3600 + timeRemaining.minutes * 60 + timeRemaining.seconds) * 1000;
    const elapsed = totalDuration - remainingMs;

    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
  }

  // Initialize the timeRemaining state when the component mounts or estimatedArrivalTime changes
  useEffect(() => {
    if (!estimatedArrivalTime) {
      setTimeRemaining(null);
      return;
    }

    try {
      // Calculate initial time remaining
      const initialTime = calculateInitialTimeRemaining();

      if (!initialTime) {
        setTimeRemaining(null);
        return;
      }

      setTimeRemaining({
        hours: initialTime.hours,
        minutes: initialTime.minutes,
        seconds: initialTime.seconds
      });

      // If already completed, set the completed flag
      if (initialTime.completed) {
        setIsCompleted(true);
        return; // Don't start the animation frame if already completed
      }
    } catch (error) {
      console.error("Error calculating initial time remaining:", error);
      setTimeRemaining(null);
      return;
    }

    // Set up interval for countdown using requestAnimationFrame
    let frameId;
    let lastUpdateTime = Date.now();

    const updateCountdown = () => {
      const now = Date.now();
      // Only update the state if at least 1 second has passed
      if (now - lastUpdateTime >= 1000) {
        const remaining = calculateTimeRemaining();
        if (remaining) {
          setTimeRemaining(remaining);
          lastUpdateTime = now;

          // Check if travel is completed
          if (remaining.hours === 0 && remaining.minutes === 0 && remaining.seconds === 0) {
            setIsCompleted(true);
            return; // Stop the animation frame loop
          }
        }
      }

      frameId = requestAnimationFrame(updateCountdown);
    };

    frameId = requestAnimationFrame(updateCountdown);

    return () => {
      if (frameId) {
        cancelAnimationFrame(frameId);
      }
    };
  }, [estimatedArrivalTime]);

  return {
    timeRemaining,
    formattedTime: getFormattedTime(),
    progressPercentage: getProgressPercentage(),
    isCompleted
  };
}

export default useTravelCountdown;
