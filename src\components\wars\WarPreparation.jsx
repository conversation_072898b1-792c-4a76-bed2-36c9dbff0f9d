import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { PreparationType } from '../../types/warPreparation';
import useAuthStore from '../../store/useAuthStore';
import { toast } from 'react-toastify';

const WarPreparation = ({ warId, onPreparationComplete }) => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [preparationHistory, setPreparationHistory] = useState([]);
  const [formData, setFormData] = useState({
    type: PreparationType.TROOPS,
    amount: 100,
    resources: {
      gold: 0,
      ammunition: 0,
      food: 0,
      fuel: 0
    }
  });

  // Calculate resource costs based on preparation type and amount
  useEffect(() => {
    const calculateResourceCosts = () => {
      const { type, amount } = formData;
      let resources = { gold: 0, ammunition: 0, food: 0, fuel: 0 };

      switch (type) {
        case PreparationType.TROOPS:
          resources.gold = Math.round(amount * 2);
          resources.food = Math.round(amount * 0.5);
          break;
        case PreparationType.SUPPLIES:
          resources.gold = Math.round(amount * 1);
          resources.ammunition = Math.round(amount * 0.3);
          resources.fuel = Math.round(amount * 0.2);
          break;
        case PreparationType.FORTIFICATION:
          resources.gold = Math.round(amount * 3);
          resources.ammunition = Math.round(amount * 0.1);
          break;
        default:
          break;
      }

      setFormData(prev => ({
        ...prev,
        resources
      }));
    };

    calculateResourceCosts();
  }, [formData.type, formData.amount]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'amount') {
      setFormData(prev => ({
        ...prev,
        amount: Math.max(1, parseInt(value) || 0)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await warService.prepareForWar(warId, user.id, formData);
      toast.success(`War preparation complete! Bonus applied: ${result.bonusApplied}`);
      
      // Add to history
      setPreparationHistory(prev => [result, ...prev]);
      
      // Notify parent component
      if (onPreparationComplete) {
        onPreparationComplete(result);
      }
    } catch (error) {
      console.error('Failed to prepare for war:', error);
      toast.error(error.response?.data?.message || 'Failed to prepare for war');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">Prepare for War</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="type" className="block text-sm font-medium text-gray-300 mb-1">
            Preparation Type
          </label>
          <select
            id="type"
            name="type"
            value={formData.type}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-white focus:outline-none focus:ring-neonBlue focus:border-neonBlue"
          >
            <option value={PreparationType.TROOPS}>Troops</option>
            <option value={PreparationType.SUPPLIES}>Supplies</option>
            <option value={PreparationType.FORTIFICATION}>Fortification</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="amount" className="block text-sm font-medium text-gray-300 mb-1">
            Amount
          </label>
          <input
            type="number"
            id="amount"
            name="amount"
            min="1"
            value={formData.amount}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-white focus:outline-none focus:ring-neonBlue focus:border-neonBlue"
          />
        </div>
        
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-md font-medium text-gray-300 mb-2">Resource Cost</h3>
          <div className="grid grid-cols-2 gap-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Gold:</span>
              <span className="text-yellow-400">{formData.resources.gold}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Ammunition:</span>
              <span className="text-red-400">{formData.resources.ammunition}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Food:</span>
              <span className="text-green-400">{formData.resources.food}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Fuel:</span>
              <span className="text-blue-400">{formData.resources.fuel}</span>
            </div>
          </div>
        </div>
        
        <button
          type="submit"
          disabled={loading}
          className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
        >
          {loading ? 'Processing...' : 'Prepare for War'}
        </button>
      </form>
      
      {preparationHistory.length > 0 && (
        <div className="mt-6">
          <h3 className="text-md font-medium text-gray-300 mb-2">Recent Preparations</h3>
          <div className="space-y-2">
            {preparationHistory.map((prep, index) => (
              <div key={prep.id || index} className="bg-gray-700 p-3 rounded-md">
                <div className="flex justify-between items-center">
                  <span className="text-white font-medium">{prep.preparationType}</span>
                  <span className="text-xs text-gray-400">
                    {new Date(prep.timestamp).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-gray-400">Amount:</span>
                  <span className="text-white">{prep.amount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Bonus Applied:</span>
                  <span className="text-green-400">+{prep.bonusApplied}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WarPreparation;
