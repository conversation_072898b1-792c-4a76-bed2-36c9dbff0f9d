import React, { useState, useEffect } from "react";
import { travelService } from "../../services/api/travel.service";
import { showErrorToast } from "../../utils/showErrorToast";
import { showSuccessToast } from "../../utils/showSuccessToast";
import { FaMapMarkedAlt, FaHourglass, FaTimes } from "react-icons/fa";
import useTravelCountdown from "../../hooks/useTravelCountdown";

const TravelStatus = ({ onTravelComplete }) => {
  const [currentTravel, setCurrentTravel] = useState(null);
  const [loading, setLoading] = useState(true);
  const [cancellingTravel, setCancellingTravel] = useState(false);

  // Use our custom hook for countdown
  const {
    timeRemaining,
    formattedTime,
    progressPercentage,
    isCompleted
  } = useTravelCountdown(currentTravel?.endTime);

  const fetchCurrentTravel = async () => {
    try {
      setLoading(true);
      const travel = await travelService.getCurrentTravel();
      setCurrentTravel(travel);

      if (travel && travel.status === 'COMPLETED' && onTravelComplete) {
        onTravelComplete();
      }
    } catch (err) {
      console.error("Error fetching travel status:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelTravel = async () => {
    if (!currentTravel) return;

    try {
      setCancellingTravel(true);
      await travelService.cancelTravel(currentTravel.id);
      showSuccessToast("Travel cancelled successfully");
      fetchCurrentTravel();
    } catch (err) {
      console.error("Error cancelling travel:", err);
      showErrorToast("Failed to cancel travel. Please try again.");
    } finally {
      setCancellingTravel(false);
    }
  };

  // Check if travel is completed and trigger callback
  useEffect(() => {
    if (isCompleted && currentTravel?.status === 'in_progress') {
      // Only fetch once when travel completes
      fetchCurrentTravel();
      if (onTravelComplete) {
        onTravelComplete();
      }
    }
  }, [isCompleted, currentTravel, onTravelComplete]);

  useEffect(() => {
    // Initial fetch when component mounts
    fetchCurrentTravel();

    // We don't need to poll the server frequently since we have the countdown timer
    // The timer is calculated client-side based on the endTime
    // Only fetch again when the travel should be complete
    let timeoutId;

    if (currentTravel?.status === 'in_progress' && currentTravel?.endTime) {
      const endTime = new Date(currentTravel.endTime).getTime();
      const now = new Date().getTime();
      const timeUntilCompletion = Math.max(0, endTime - now);

      // Set a single timeout to check status when travel should complete
      // Add 1 second buffer to ensure the server has processed the completion
      timeoutId = setTimeout(() => {
        fetchCurrentTravel();
      }, timeUntilCompletion + 1000);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [currentTravel?.status, currentTravel?.endTime]);

  if (loading) {
    return (
      <div className="bg-gray-700/30 rounded-lg p-4 animate-pulse">
        <div className="h-6 bg-gray-600 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-600 rounded w-1/2"></div>
      </div>
    );
  }

  if (!currentTravel || currentTravel.status !== 'in_progress') {
    return null;
  }

  return (
    <div className="bg-blue-900/30 rounded-lg p-4">
      <div className="flex flex-col">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold text-white flex items-center">
              <FaMapMarkedAlt className="mr-2 text-blue-400" /> Traveling
            </h3>
            <p className="text-gray-300 mt-1">
              From: <span className="text-white">{currentTravel.sourceRegion?.name || "Unknown"}</span>
            </p>
            <p className="text-gray-300">
              To: <span className="text-white">{currentTravel.destinationRegion?.name || "Unknown"}</span>
            </p>
          </div>

          <button
            onClick={handleCancelTravel}
            disabled={cancellingTravel}
            className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-md flex items-center disabled:bg-gray-600 disabled:text-gray-400"
          >
            <FaTimes className="mr-1" />
            {cancellingTravel ? "Cancelling..." : "Cancel"}
          </button>
        </div>

        {/* Countdown Timer - Similar to Profile.jsx */}
        {timeRemaining && (
          <div className="mt-4 bg-gray-800/50 rounded-lg p-3 text-center">
            <div className="text-sm text-gray-400 mb-1 flex items-center justify-center">
              <FaHourglass className="mr-2" /> Estimated Arrival Time
            </div>

            <div className="mb-2">
              <div className={`inline-flex bg-gray-800 rounded-lg px-3 py-1 font-mono font-semibold text-white ${
                timeRemaining.hours === 0 && timeRemaining.minutes === 0 ? 'animate-pulse bg-red-900/50' : ''
              }`}>
                {timeRemaining.hours > 0 && (
                  <span className="mx-1 text-blue-400">{timeRemaining.hours}h</span>
                )}
                {(timeRemaining.hours > 0 || timeRemaining.minutes > 0) && (
                  <span className="mx-1 text-blue-400">{timeRemaining.minutes}m</span>
                )}
                <span className={`mx-1 ${
                  timeRemaining.hours === 0 && timeRemaining.minutes === 0
                    ? 'text-red-500 text-lg'
                    : 'text-red-400'
                }`}>
                  {timeRemaining.seconds}s
                </span>
              </div>
            </div>

            <div className="w-full bg-gray-800 rounded-full h-3 mb-2">
              <div
                className="bg-blue-500 h-3 rounded-full relative overflow-hidden transition-all duration-1000 ease-linear"
                style={{ width: `${progressPercentage}%` }}
              >
                <div className="absolute inset-0 bg-white opacity-20 animate-pulse"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-shimmer" style={{ animation: "shimmer 2s infinite" }}></div>
              </div>
            </div>

            <p className="text-gray-300 text-xs">
              Arrival: {new Date(currentTravel.endTime).toLocaleTimeString()}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TravelStatus;
