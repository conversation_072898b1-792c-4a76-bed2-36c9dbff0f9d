import { WarType, WarTarget } from './war';

export interface WarStatistics {
  totalWars: number;
  activeWars: number;
  endedWars: number;
  groundWars: number;
  seaWars: number;
  revolutionWars: number;
  conquestWars: number;
  resourceWars: number;
  averageDuration: number;
  mostActiveRegions: Array<{ regionId: string; regionName: string; warCount: number }>;
  mostActiveStates: Array<{ stateId: string; stateName: string; warCount: number }>;
}

export interface UserWarStatistics {
  totalParticipation: number;
  warsWon: number;
  warsLost: number;
  totalDamageDealt: number;
  highestDamageInSingleWar: number;
  mostActiveWarId: string;
  currentActiveWars: number;
}

export interface WarTimelineEvent {
  timestamp: Date;
  description: string;
  warId: string;
  warName: string;
  eventType: string;
}

export interface RegionWarHistory {
  regionId: string;
  regionName: string;
  warsAsAttacker: number;
  warsAsDefender: number;
  conquestsWon: number;
  conquestsLost: number;
  resourceWarsWon: number;
  resourceWarsLost: number;
  revolutionsWon: number;
  revolutionsLost: number;
}

export interface DamageLeaderboard {
  userId: number;
  username: string;
  totalDamage: number;
  warCount: number;
  averageDamagePerWar: number;
  highestSingleWarDamage: number;
  side: 'attacker' | 'defender' | 'mixed';
}

export interface EfficiencyMetrics {
  userId: number;
  username: string;
  totalDamage: number;
  totalEnergySpent: number;
  efficiency: number;
  warCount: number;
  winCount: number;
  winRate: number;
}

export interface RegionalPerformance {
  regionId: string;
  regionName: string;
  totalWars: number;
  warsWon: number;
  warsLost: number;
  winRate: number;
  totalDamageDealt: number;
  totalDamageReceived: number;
  damageRatio: number;
  mostActiveWarriors: Array<{
    userId: number;
    username: string;
    damage: number;
  }>;
}

export interface WarTrends {
  timeframe: string;
  totalWars: number;
  attackerWinRate: number;
  defenderWinRate: number;
  averageDuration: number;
  averageParticipants: number;
  mostCommonType: WarType;
  mostCommonTarget: WarTarget;
}
