import React, { useState, useEffect } from 'react';
import { Vote, Calendar, AlertCircle, Users, TrendingUp } from 'lucide-react';
import ActiveElection from './ActiveElection';
import ElectionHistory from './ElectionHistory';
import useElectionStore from '../../store/useElectionStore';
import useUserDataStore from '../../store/useUserDataStore';
import { stateService } from '../../services/api/state.service';
import { showErrorToast } from '../../utils/showErrorToast';

const ElectionDashboard = () => {
  const [activeTab, setActiveTab] = useState('current');
  const [userState, setUserState] = useState(null);
  const [loading, setLoading] = useState(true);
  const { activeElection, fetchActiveElection, clearElectionData } = useElectionStore();
  const { userData } = useUserDataStore();

  useEffect(() => {
    const fetchUserState = async () => {
      try {
        setLoading(true);
        
        // First try to get the state the user leads
        let state = null;
        try {
          if (userData?.region?.state) {
            state = userData.region.state;
          } else {
            state = await stateService.getStateLedByUser();
          }
        } catch (error) {
          // User doesn't lead a state, try to get their region's state
          if (userData?.region?.state) {
            state = userData.region.state;
          }
        }

        // Clear previous election data when state changes
        if (state?.id !== userState?.id) {
          clearElectionData();
        }

        if (state) {
          setUserState(state);
          // Fetch active election for this state
          await fetchActiveElection(state.id);
        } else {
          setUserState(null);
        }
      } catch (error) {
        console.error('Failed to fetch user state:', error);
        showErrorToast('Failed to load state information');
      } finally {
        setLoading(false);
      }
    };

    if (userData) {
      fetchUserState();
    }
  }, [userData, fetchActiveElection, clearElectionData]);

  const tabs = [
    { id: 'current', label: 'Current Election', icon: Vote },
    { id: 'history', label: 'Election History', icon: Calendar }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-700 rounded w-1/3"></div>
            <div className="h-64 bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // If user is not in a state
  if (!userState) {
    return (
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <h1 className="text-3xl font-bold text-white mb-8">State Elections</h1>
            
            <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <AlertCircle className="w-6 h-6 text-yellow-400" />
                <h2 className="text-lg font-semibold text-white">No State Affiliation</h2>
              </div>
              <p className="text-gray-300 mb-4">
                You are not currently affiliated with any state. To participate in state elections, you need to:
              </p>
              <ul className="list-disc list-inside text-gray-300 space-y-2 mb-4">
                <li>Be a resident of a region that belongs to a state</li>
                <li>Or be the leader of a state</li>
              </ul>
              <p className="text-gray-400 text-sm">
                Contact your region's leadership or consider creating your own state to participate in elections.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">State Elections</h1>
            <p className="text-gray-400">
              Participate in democratic elections for {userState.name}
            </p>
          </div>

          {/* State Info */}
          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
                  {userState.flagUrl ? (
                    <img
                      src={userState.flagUrl}
                      alt={`${userState.name} flag`}
                      className="w-8 h-8 object-cover rounded"
                    />
                  ) : (
                    <Users className="w-6 h-6 text-gray-400" />
                  )}
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-white">{userState.name}</h2>
                  <p className="text-gray-400 text-sm">
                    Led by 
                    {userState.leader?.username ? (
                      <a
                        href={`/users/${userState.leader.id}`}
                        className="text-blue-400 hover:text-blue-300"
                      >
                        {" "+userState.leader.username} • {userState.regions?.length} regions
                      </a>
                    ) : (
                      ' Unknown'
                    )}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-400">Your Role</div>
                <div className="text-white font-medium">
                  {userData?.id === userState?.leader?.id ? 'State Leader' : 'Citizen'}
                </div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-gray-800 rounded-lg mb-6">
            <div className="flex border-b border-gray-700">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      flex items-center space-x-2 px-6 py-4 font-medium transition-colors
                      ${activeTab === tab.id
                        ? 'text-blue-400 border-b-2 border-blue-400 bg-blue-900/20'
                        : 'text-gray-400 hover:text-gray-300'
                      }
                    `}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === 'current' && (
            <div>
              {activeElection ? (
                <ActiveElection 
                  election={activeElection} 
                  stateId={userState.id}
                />
              ) : (
                <div className="bg-gray-800 rounded-lg p-8 text-center">
                  <Calendar className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">No Active Election</h3>
                  <p className="text-gray-400 mb-4">
                    There is currently no active election in {userState.name}.
                  </p>
                  <p className="text-gray-500 text-sm">
                    Elections are typically held every few months. Check back later or view past elections in the history tab.
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'history' && (
            <ElectionHistory stateId={userState.id} />
          )}
        </div>
      </div>
    </div>
  );
};

export default ElectionDashboard;



