import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { toast } from 'react-toastify';

const WarMorale = ({ warId }) => {
  const [loading, setLoading] = useState(true);
  const [moraleData, setMoraleData] = useState(null);

  useEffect(() => {
    fetchMoraleData();
  }, [warId]);

  const fetchMoraleData = async () => {
    setLoading(true);
    try {
      const data = await warService.getWarMorale(warId);
      setMoraleData(data);
    } catch (error) {
      console.error('Failed to fetch war morale:', error);
      toast.error('Failed to load war morale data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="text-center py-8"><div className="loader">Loading morale data...</div></div>;
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">War Morale</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Attacker Morale */}
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Attacker Morale</h3>
          <div className="relative pt-1">
            <div className="flex mb-2 items-center justify-between">
              <div>
                <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200">
                  Morale
                </span>
              </div>
              <div className="text-right">
                <span className="text-xs font-semibold inline-block text-blue-600">
                  {moraleData.attackerMorale}%
                </span>
              </div>
            </div>
            <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200">
              <div 
                style={{ width: `${moraleData.attackerMorale}%` }} 
                className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500"
              ></div>
            </div>
          </div>
        </div>
        
        {/* Defender Morale */}
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Defender Morale</h3>
          <div className="relative pt-1">
            <div className="flex mb-2 items-center justify-between">
              <div>
                <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-red-600 bg-red-200">
                  Morale
                </span>
              </div>
              <div className="text-right">
                <span className="text-xs font-semibold inline-block text-red-600">
                  {moraleData.defenderMorale}%
                </span>
              </div>
            </div>
            <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-red-200">
              <div 
                style={{ width: `${moraleData.defenderMorale}%` }} 
                className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-red-500"
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Recent Events */}
      <div>
        <h3 className="text-lg font-medium text-white mb-3">Recent Morale Events</h3>
        {moraleData.recentEvents && moraleData.recentEvents.length > 0 ? (
          <div className="space-y-3">
            {moraleData.recentEvents.map((event, index) => (
              <div key={index} className="bg-gray-700 p-3 rounded-md">
                <div className="flex justify-between items-center">
                  <span className="text-white font-medium">{event.type}</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    event.moraleImpact > 0 
                      ? 'bg-green-900 text-green-300' 
                      : 'bg-red-900 text-red-300'
                  }`}>
                    {event.moraleImpact > 0 ? '+' : ''}{event.moraleImpact}
                  </span>
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {new Date(event.timestamp).toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-gray-400 text-center py-4">No recent morale events</div>
        )}
      </div>
      
      {/* Morale Info */}
      <div className="mt-6 bg-gray-700 p-4 rounded-md">
        <h3 className="text-md font-medium text-white mb-2">About Morale</h3>
        <p className="text-gray-300 text-sm">
          Morale affects combat effectiveness. High morale provides bonuses to your troops, while low morale can severely impact performance. 
          Morale is affected by battle outcomes, supply levels, and strategic decisions.
        </p>
      </div>
    </div>
  );
};

export default WarMorale;
