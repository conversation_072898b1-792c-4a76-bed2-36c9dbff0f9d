import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { WarType, WarTarget } from '../../types/war.ts';
import useAuthStore from '../../store/useAuthStore';
import useUserDataStore from '../../store/useUserDataStore';
import { api } from '../../services/api/api';
import { regionService } from '../../services/api/region.service';
import { stateService } from '../../services/api/state.service';
import { warService } from '../../services/api/war.service';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { showErrorToast } from '../../utils/showErrorToast';

const DeclareWarForm = ({ onSuccess }) => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { userData, fetchUserData } = useUserDataStore();
  const [loading, setLoading] = useState(false);
  const [redirecting, setRedirecting] = useState(false);
  const [regions, setRegions] = useState([]);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    attackerRegionId: '',
    defenderRegionId: '',
    targetRegionName: '',
    declaration: ''
  });
    const [isStateLeader, setIsStateLeader] = useState(false);
  // This state is used for the search functionality which is currently commented out
  // Keeping it for future reference
  // const [targetRegion, setTargetRegion] = useState(null);

  // Fetch user data to ensure we have the latest information
  useEffect(() => {
    if (!user?.id) return;

    // Use the centralized user data store
    fetchUserData();

  }, [user?.id, fetchUserData, userData]);

  // Fetch user's state ID directly
  const [stateId, setStateId] = useState(null);

  // Fetch user's state ID
  useEffect(() => {
    const fetchUserStateId = async () => {
      if (!user?.id) return;

      try {
        // Directly fetch the user's data to get the state ID
        const state = await stateService.getStateLedByUser();
        // Check if the user has a state
        if (state) {
          setStateId(state.id);
          setRegions(state.regions);
          setIsStateLeader(true);
        } else {
          setStateId(null);
        }
      } catch (err) {
        // Handle error silently
        setError('Failed to fetch user data');
        setStateId(null);
      }
    };

    fetchUserStateId();
  }, [user?.id]);

  // Fetch regions data
  useEffect(() => {
    // Only fetch regions if we have a state ID
    if (!stateId) return;

    const fetchData = async () => {
      try {
        const regionsResponse = await api.get('/regions');
        // Set all regions
        setRegions(regionsResponse.data);
      } catch (err) {
        setError('Failed to load data');
        showErrorToast('Failed to load regions');
      }
    };
    fetchData();
  }, [stateId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Reset defender region and target region name when attacker region changes
    if (name === 'attackerRegionId') {
      setFormData(prev => ({
        ...prev,
        defenderRegionId: '',
        targetRegionName: ''
      }));
      // setTargetRegion(null);
    }
  };

  // This function is used in the commented out search field
  // Keeping it for future reference if the search functionality is re-enabled
  /*
  const handleTargetRegionNameChange = (e) => {
    const { value } = e.target;
    setFormData(prev => ({
      ...prev,
      targetRegionName: value,
      defenderRegionId: ''
    }));
    setTargetRegion(null);
  };
  */

  // Search for region by name
  const searchRegion = async () => {
    if (!formData.targetRegionName.trim()) {
      return;
    }

    try {
      const results = await regionService.searchRegionsByName(formData.targetRegionName);
      if (results && results.length > 0) {
        // Find the first region that matches the name exactly or closely
        const foundRegion = results.find(r =>
          r.name.toLowerCase() === formData.targetRegionName.toLowerCase()
        ) || results[0];

        // Check if this region borders with the attacker region
        const attackerRegion = regions.find(r => r.id === formData.attackerRegionId);
        if (!attackerRegion) {
          showErrorToast('Please select an attacking region first');
          return;
        }

        // Check if the found region is in the bordering regions using bordersWithNames if available
        if (attackerRegion.bordersWithNames) {
          const isValidTarget = attackerRegion.bordersWithNames.some(name =>
            name.toLowerCase() === foundRegion.name.toLowerCase()
          );

          if (!isValidTarget) {
            showErrorToast(`${foundRegion.name} does not border with ${attackerRegion.name}`);
            return;
          }
        }
        // Fallback to using bordersWith country codes if bordersWithNames is not available
        else if (!attackerRegion.bordersWith?.includes(foundRegion.countryCode)) {
          showErrorToast(`${foundRegion.name} does not border with ${attackerRegion.name}`);
          return;
        }

        // If attacker has sea access, target must also have sea access
        if (attackerRegion.seaAccess && !foundRegion.seaAccess) {
          showErrorToast(`${foundRegion.name} does not have sea access, which is required for this attack`);
          return;
        }

        // Can't attack regions in the same state
        if (foundRegion.state?.id === stateId) {
          showErrorToast(`${foundRegion.name} belongs to your state. You cannot attack your own state's regions.`);
          return;
        }

        // Set the found region as the target
        // setTargetRegion(foundRegion); // Commented out as we're not using this state currently
        setFormData(prev => ({
          ...prev,
          defenderRegionId: foundRegion.id
        }));
        showSuccessToast(`Found region: ${foundRegion.name}`);
      } else {
        showErrorToast(`No region found with name: ${formData.targetRegionName}`);
      }
    } catch (error) {
      console.error('Error searching for region:', error);
      showErrorToast('Failed to search for region');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // If we don't have a target region selected, try to search for it
      if (!formData.defenderRegionId && formData.targetRegionName) {
        await searchRegion();
        // If after searching we still don't have a defender region ID, stop submission
        if (!formData.defenderRegionId) {
          throw new Error('Please select a valid target region');
        }
      }

      const attackerRegion = regions.find(r => r.id === formData.attackerRegionId);
      const defenderRegion = regions.find(r => r.id === formData.defenderRegionId);

      if (!attackerRegion) {
        throw new Error('Please select an attacking region');
      }

      if (!defenderRegion) {
        throw new Error('Please select a valid target region');
      }

      // Validate bordering regions
      if (attackerRegion.bordersWithNames) {
        const isValidTarget = attackerRegion.bordersWithNames.some(name =>
          name.toLowerCase() === defenderRegion.name.toLowerCase()
        );

        if (!isValidTarget) {
          throw new Error(`${defenderRegion.name} does not border with ${attackerRegion.name}`);
        }
      }
      // Fallback to using bordersWith country codes
      else if (!attackerRegion.bordersWith?.includes(defenderRegion.countryCode)) {
        throw new Error(`${defenderRegion.name} does not border with ${attackerRegion.name}`);
      }

      const warData = {
        warType: WarType.GROUND,
        warTarget: WarTarget.CONQUEST,
        declaration: formData.declaration,
        attackerRegionId: formData.attackerRegionId,
        defenderRegionId: formData.defenderRegionId,
      };

      try {
        // Get the war data from the response
        const createdWar = await warService.declareWar(warData);

        // Show success message with more details
        showSuccessToast(`War declared successfully against ${defenderRegion.name}!`);

        // Store the created war ID in localStorage to handle potential network issues
        if (createdWar && createdWar.id) {
          localStorage.setItem('lastCreatedWarId', createdWar.id);
        }

        // Call onSuccess with the created war if it exists
        if (onSuccess) {
          onSuccess(createdWar);
        } else {
          // Only navigate if onSuccess is not provided
          // This avoids network errors when trying to fetch the specific war
          navigate('/wars');
        }
      } catch (declareError) {
        console.error('Error during war declaration:', declareError);
        throw declareError; // Re-throw to be caught by the outer catch block
      }
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to declare war');
      showErrorToast(err);
      setRedirecting(false); // Reset redirecting state on error
    } finally {
      setLoading(false);
    }
  };

  const getAvailableTargetRegions = (attackerRegionId) => {
    if (!attackerRegionId) {
      return [];
    }

    const attackerRegion = regions.find(r => r.id === attackerRegionId);
    if (!attackerRegion) {
      return [];
    }

    // Try using bordersWithNames to find target regions
    if (attackerRegion.bordersWithNames && Array.isArray(attackerRegion.bordersWithNames)) {
      const targetRegions = [];

      // For each bordering region name, find the corresponding region object
      attackerRegion.bordersWithNames.forEach(name => {
        // Find regions that match this name
        const matchingRegions = regions.filter(r =>
          r.name.toLowerCase() === name.toLowerCase() &&
          r.state?.id !== stateId // Not in the same state
        );

        if (matchingRegions.length > 0) {
          targetRegions.push(...matchingRegions);
        }
      });

      return targetRegions;
    }

    // Fallback to using bordersWith country codes
    const targetRegions = regions.filter(region => {
      // Must be a bordering region
      if (!attackerRegion.bordersWith?.includes(region.countryCode)) {
        return false;
      }

      // If attacker has sea access, target must also have sea access
      if (attackerRegion.seaAccess && !region.seaAccess) {
        return false;
      }

      // Can't attack own region
      if (region.id === attackerRegionId) {
        return false;
      }

      // Can't attack regions in the same state
      if (region.state?.id === stateId) {
        return false;
      }

      return true;
    });

    return targetRegions;
  };

  // Get the names of bordering regions for display
  const getBorderingRegionNames = (attackerRegionId) => {
    if (!attackerRegionId) return [];
    const attackerRegion = regions.find(r => r.id === attackerRegionId);
    if (!attackerRegion) return [];

    // Use the bordersWithNames property if available
    if (attackerRegion.bordersWithNames && Array.isArray(attackerRegion.bordersWithNames)) {
      // Filter out regions that are in the same state
      return attackerRegion.bordersWithNames.filter(name => {
        // Find the region with this name
        const matchingRegion = regions.find(r => r.name.toLowerCase() === name.toLowerCase());
        // Only include regions that are not in the user's state
        return !matchingRegion || matchingRegion.state?.id !== stateId;
      });
    }

    // Fallback to the old method if bordersWithNames is not available
    if (!attackerRegion.bordersWith) return [];

    return attackerRegion.bordersWith
      .map(id => {
        const region = regions.find(r => r.id === id);
        // Only include regions that are not in the user's state
        if (region && region.state?.id !== stateId) {
          return region.name; // Return just the name, not the whole object
        }
        return '';
      })
      .filter(name => name); // Filter out empty names
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">Declare War</h2>
      {!isStateLeader ? (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
          <p>You must be the leader of your state to declare war.</p>
        </div>
      ) : (
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="attackerRegionId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Attacking Region
          </label>
          <select
            id="attackerRegionId"
            name="attackerRegionId"
            value={formData.attackerRegionId}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
            required
          >
            <option value="">Select your region</option>
            {/* Filter regions to only show those in the user's state */}
            {regions
              .filter(region => region.state?.id === stateId)
              .map(region => (
                <option key={region.id} value={region.id}>
                  {region.name}
                </option>
              ))}
          </select>
        </div>

        {formData.attackerRegionId && (
          <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md mb-4">
            <h4 className="font-medium text-gray-800 dark:text-white mb-2">Potential Target Regions</h4>
            <p className="text-gray-700 dark:text-gray-300 mb-2">Your region borders with these regions from other states:</p>
            {getBorderingRegionNames(formData.attackerRegionId).length > 0 ? (
              <ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
                {getBorderingRegionNames(formData.attackerRegionId).map((name, index) => (
                  <li key={index}>{name}</li>
                ))}
              </ul>
            ) : (
              <p className="text-yellow-600 dark:text-yellow-400 italic">No valid target regions found. Your region may only border with regions from your own state.</p>
            )}
          </div>
        )}

        <div>
          <label htmlFor="defenderRegionId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Select Target Region from List
          </label>
          <select
            id="defenderRegionId"
            name="defenderRegionId"
            value={formData.defenderRegionId}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
            disabled={!formData.attackerRegionId}
          >
            <option value="">Select target region</option>
            {/* {getBorderingRegionNames(formData.attackerRegionId).map((name, index) => (
                  <li key={index}>{name}</li>
                ))} */}
            {getAvailableTargetRegions(formData.attackerRegionId).map(region => (
              <option key={region.id} value={region.id}>
                {region.name}
              </option>
            ))}
          </select>
        </div>

        {/* <div>
          <label htmlFor="declaration" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            War Declaration
          </label>
          <textarea
            id="declaration"
            name="declaration"
            value={formData.declaration}
            onChange={handleChange}
            rows="4"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
            placeholder="Enter your war declaration..."
            maxLength="500"
          ></textarea>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
            {formData.declaration.length}/500
          </div>
        </div> */}

        <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md mb-6">
          <h4 className="font-medium text-gray-800 dark:text-white mb-2">War Information</h4>
          <ol className="list-decimal pl-5 text-gray-700 dark:text-gray-300">
            <li>Ground Phase (24 hours)</li>
          </ol>
        </div>

        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <p>{error}</p>
          </div>
        )}

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading || redirecting}
            className={`px-4 py-2 bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 ${
              (loading || redirecting) ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {loading ? 'Declaring War...' : redirecting ? 'Redirecting...' : 'Declare War'}
          </button>
        </div>
      </form>
      )}

    </div>
  );
};

export default DeclareWarForm;