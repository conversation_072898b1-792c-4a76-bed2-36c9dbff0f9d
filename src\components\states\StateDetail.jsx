import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import { stateService } from "../../services/api/state.service";
import Navbar from "../Navbar";
import useUserDataStore from "../../store/useUserDataStore";
import {
  FaCheck,
  FaTimes,
  FaEdit,
} from "react-icons/fa";
import { showErrorToast } from "../../utils/showErrorToast";
import { showSuccessToast } from "../../utils/showSuccessToast";

const StateDetail = () => {
  const { id } = useParams();
  const [state, setState] = useState(null);
  const [resources, setResources] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { userData: user, fetchUserData } = useUserDataStore();
  const [isEditingName, setIsEditingName] = useState(false);
  const [newName, setNewName] = useState("");
  const [isUpdatingName, setIsUpdatingName] = useState(false);
  // Check if user is the state leader
  const isStateLeader = state?.leader?.id === user?.id;

  useEffect(() => {
    const fetchStateData = async () => {
      try {
        setLoading(true);
        const [stateData, resourcesData] = await Promise.all([
          stateService.getState(id),
          stateService.getStateResources(id),
        ]);
        setState(stateData);
        setResources(resourcesData);
        setError(null);
      } catch (err) {
        console.error("Error fetching state details:", err);
        setError("Failed to load state details. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchStateData();
  }, [id]);

  // State editing functions
  const handleEditName = () => {
    setNewName(state?.name || "");
    setIsEditingName(true);
  };

  const handleCancelEdit = () => {
    setIsEditingName(false);
    setNewName("");
  };

  const handleSaveName = async () => {
    if (!newName.trim()) {
      showErrorToast("Name cannot be empty");
      return;
    }

    if (newName === state?.name) {
      setIsEditingName(false);
      return;
    }

    setIsUpdatingName(true);
    try {
      await stateService.updateState(state.id,{
        name: newName.trim(),
      });
      showSuccessToast("Name updated successfully!");
      state.name = newName.trim();
      setIsEditingName(false);
      setNewName("");
    } catch (error) {
      showErrorToast(error || "Failed to update Name");
    } finally {
      setIsUpdatingName(false);
    }
  };

  const handleNameKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSaveName();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  if (loading)
    return (
      <>
        <Navbar />
        <div className="flex justify-center items-center min-h-screen bg-gray-900">
          <div className="loader text-blue-500">Loading...</div>
        </div>
      </>
    );

  if (error)
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gray-900 p-4">
          <div className="text-red-500 bg-red-900/20 p-4 rounded-lg">
            {error}
          </div>
        </div>
      </>
    );

  if (!state)
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gray-900 p-4">
          <div className="text-gray-300 bg-gray-800/50 p-4 rounded-lg">
            State not found
          </div>
        </div>
      </>
    );

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gray-900 p-4">
        <div className="max-w-6xl mx-auto">
          <div className="mb-6">
            <Link
              to="/states"
              className="text-blue-500 hover:text-blue-400 flex items-center"
            >
              <span className="mr-2">←</span> Back to States
            </Link>
          </div>

          <div className="bg-gray-800 rounded-lg shadow-xl p-6">
            {/* Header Section */}
            <div className="flex items-center mb-8">
              {state.flagUrl && (
                <img
                  src={state.flagUrl}
                  alt={`${state.name} flag`}
                  className="w-20 h-20 object-cover rounded-lg mr-6 border-2 border-gray-700"
                />
              )}
              <div>
                {isEditingName ? (
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={newName}
                      onChange={(e) => setNewName(e.target.value)}
                      onKeyDown={handleNameKeyPress}
                      className="w-30 font-bold bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 focus:border-neonBlue focus:outline-none"
                      placeholder="Enter Name"
                      maxLength={20}
                      disabled={isUpdatingName}
                    />
                    <button
                      onClick={handleSaveName}
                      disabled={isUpdatingName}
                      className="text-green-400 hover:text-green-300 p-2 disabled:opacity-50"
                      title="Save Name"
                    >
                      <FaCheck />
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      disabled={isUpdatingName}
                      className="text-red-400 hover:text-red-300 p-2 disabled:opacity-50"
                      title="Cancel edit"
                    >
                      <FaTimes />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <h1 className="text-3xl font-bold text-neonBlue">
                      {state?.name}
                    </h1>
                    {isStateLeader && (
                      <button
                        onClick={handleEditName}
                        className="text-gray-400 hover:text-neonBlue p-1 ml-2"
                        title="Edit Name"
                      >
                        <FaEdit />
                      </button>
                    )}
                  </div>
                )}
                <p className="text-gray-400">
                  Led by
                  {state?.leader?.username ? (
                  <Link className="p-2" to={`/users/${state?.leader?.id}`}>
                    <span className="text-blue-400">
                      {state.leader.username}
                    </span>
                  </Link>
                  ):(
                    <span className="text-gray-400"> Unknown</span>
                  )}
                </p>
              </div>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column */}
              <div>
                <div className="bg-gray-700/30 rounded-lg p-6 mb-6">
                  <h2 className="text-xl font-semibold text-white mb-4">
                    Regions ({state.regions.length})
                  </h2>
                  {state.regions.length === 0 ? (
                    <p className="text-gray-400">No regions in this state.</p>
                  ) : (
                    <div className="space-y-3">
                      {state.regions.map((region) => (
                        <div
                          key={region.id}
                          className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors"
                        >
                          <Link
                            to={`/regions/${region.id}`}
                            className="flex items-center justify-between"
                          >
                            <div>
                              <h3 className="text-white font-medium">
                                {region.name}
                              </h3>
                              <p className="text-gray-400 text-sm">
                                Population:{" "}
                                {region.population?.toLocaleString() ||
                                  "Unknown"}
                              </p>
                            </div>
                            <div className="text-blue-400">→</div>
                          </Link>

                          {/* Parties Section */}
                          <div className="mt-3 border-t border-gray-700 pt-3">
                            <h4 className="text-gray-300 text-sm font-medium mb-2">
                              Parties:
                            </h4>
                            {region.parties && region.parties.length > 0 ? (
                              <div className="space-y-2">
                                {region.parties.map((party) => (
                                  <div
                                    key={party.id}
                                    className="flex items-center"
                                  >
                                    <Link
                                      to={`/party/${party.id}`}
                                      className="text-blue-400 hover:text-blue-300 text-sm"
                                    >
                                      {party.name}
                                    </Link>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <p className="text-gray-500 text-sm italic">
                                No parties in this region
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Right Column */}
              <div>
                {resources && (
                  <div className="bg-gray-700/30 rounded-lg p-6 mb-6">
                    <h2 className="text-xl font-semibold text-white mb-4">
                      Resources
                    </h2>
                    <div className="space-y-4">
                      <div className="bg-gray-800 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-400">Treasury</span>
                          <span className="text-white font-medium">
                            {resources.treasury.toLocaleString()}
                          </span>
                        </div>
                      </div>

                      <div className="bg-gray-800 rounded-lg p-4">
                        <h3 className="text-white font-medium mb-3">
                          Resource Reserves
                        </h3>
                        <div className="grid grid-cols-2 gap-4">
                          {Object.entries(resources.resourceReserves).map(
                            ([resource, amount]) =>
                              amount > 0 && (
                                <div
                                  key={resource}
                                  className="flex justify-between items-center"
                                >
                                  <span className="text-gray-400 capitalize">
                                    {resource}
                                  </span>
                                  <span className="text-white">
                                    {amount.toLocaleString()}
                                  </span>
                                </div>
                              )
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Diplomacy Section */}
                <div className="bg-gray-700/30 rounded-lg p-6">
                  <h2 className="text-xl font-semibold text-white mb-4">
                    Diplomacy
                  </h2>
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-white font-medium mb-3">Allies</h3>
                      {!state.allies || state.allies.length === 0 ? (
                        <p className="text-gray-400">No allies</p>
                      ) : (
                        <ul className="space-y-2">
                          {state.allies.map((allyId) => (
                            <li key={allyId}>
                              <Link
                                to={`/states/${allyId}`}
                                className="text-blue-400 hover:text-blue-300"
                              >
                                View Ally
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>

                    <div>
                      <h3 className="text-white font-medium mb-3">Enemies</h3>
                      {!state.enemies || state.enemies.length === 0 ? (
                        <p className="text-gray-400">No enemies</p>
                      ) : (
                        <ul className="space-y-2">
                          {state.enemies.map((enemyId) => (
                            <li key={enemyId}>
                              <Link
                                to={`/states/${enemyId}`}
                                className="text-blue-400 hover:text-blue-300"
                              >
                                View Enemy
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default StateDetail;
