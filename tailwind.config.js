/** @type {import('tailwindcss').Config} */
export default {
    darkMode: "class", // Enable dark mode
    content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
    theme: {
      extend: {
        colors: {
          darkBg: "#0d1117",
          darkCard: "#161b22",
          neonBlue: "#3f87ff",
          neonGreen: "#00ff87",
        },
        keyframes: {
          shimmer: {
            '0%': { transform: 'translateX(-100%)' },
            '100%': { transform: 'translateX(100%)' }
          }
        },
        animation: {
          shimmer: 'shimmer 2s infinite linear'
        }
      },
    },
    plugins: [],
  };