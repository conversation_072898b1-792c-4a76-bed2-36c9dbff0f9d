import React, { useState } from "react";
import { warService } from "../../services/api/war.service";

import useUserDataStore from "../../store/useUserDataStore";
import { showSuccessToast } from "../../utils/showSuccessToast";
import { showErrorToast } from "../../utils/showErrorToast";

const WarParticipateForm = ({ warId, war, userSide, onParticipationComplete }) => {
  // We need userData for premium status check and energy amount
  const { userData, fetchUserData } = useUserDataStore();
  const [loading, setLoading] = useState(false);

  // Check if user has premium
  const isPremium = userData?.isPremium || false;

  // Get current user energy
  const currentEnergy = userData?.energy || 0;

  // Check if auto mode is currently active for war
  const isAutoModeActive = userData?.activeAutoMode === 'war';

  const handleManualAttack = async () => {
    setLoading(true);

    try {
      // Refresh user data to get the latest energy amount
      await fetchUserData(true);

      // Get the latest energy value
      const latestEnergy = userData?.energy || 0;

      if (latestEnergy <= 0) {
        showErrorToast(
          "You do not have enough energy to participate in the war."
        );
        return;
      }

      // Manual attack uses all energy
      const participateData = {
        energyAmount: latestEnergy, // Use 100% of energy
        autoMode: false,
      };
      console.log(userSide,'userSide');
      
      // Add side parameter for revolution wars
      if (war?.warType === 'revolution' && userSide) {
        participateData.side = userSide;
      }

      const result = await warService.participateInWar(warId, participateData);

      showSuccessToast("Successfully participated in war!");

      // Refresh user data to update energy display
      await fetchUserData(true);

      // Notify parent component
      if (onParticipationComplete) {
        onParticipationComplete(result);
      }
    } catch (error) {
      console.error("Failed to participate in war:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  const handleEnableAutoMode = async () => {
    setLoading(true);

    try {
      // Refresh user data to get the latest energy amount
      await fetchUserData(true);

      // Get the latest energy value
      const latestEnergy = userData?.energy || 0;

      if (latestEnergy <= 0) {
        showErrorToast(
          "You do not have enough energy to participate in the war."
        );
        return;
      }

      // Enable auto mode
      const participateData = {
        energyAmount: latestEnergy, // Not used for auto mode
        autoMode: true,
        autoEnergyPercentage: 100, // Always use 100% in auto mode
      };

      // Add side parameter for revolution wars
      if (war?.warType === 'revolution' && userSide) {
        participateData.side = userSide;
      }

      const result = await warService.participateInWar(warId, participateData);

      showSuccessToast(
        "Auto mode enabled! The system will attack every 30 minutes."
      );

      // Refresh user data to update energy display
      await fetchUserData(true);

      // Notify parent component
      if (onParticipationComplete) {
        onParticipationComplete(result);
      }
    } catch (error) {
      console.error("Failed to enable auto mode:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  const handleDisableAutoMode = async () => {
    setLoading(true);

    try {
      // Call the stop auto attack API
      const result = await warService.stopAutoAttack(warId);

      showSuccessToast(result.message || "Auto mode disabled successfully!");

      // Refresh user data to update auto mode status
      await fetchUserData(true);

      // Notify parent component
      if (onParticipationComplete) {
        onParticipationComplete();
      }
    } catch (error) {
      console.error("Failed to disable auto mode:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSwitchSide = async (newSide) => {
    if (war?.warType !== 'revolution' || newSide === userSide) return;

    setLoading(true);

    try {
      // Refresh user data to get the latest energy amount
      await fetchUserData(true);

      // Get the latest energy value
      const latestEnergy = userData?.energy || 0;

      if (latestEnergy <= 0) {
        showErrorToast("You need energy to switch sides.");
        return;
      }

      // Switch side by participating with the new side
      const participateData = {
        energyAmount: latestEnergy,
        autoMode: false,
        side: newSide
      };

      await warService.participateInWar(warId, participateData);

      showSuccessToast(`Successfully switched to ${newSide} side!`);

      // Refresh user data to update energy display
      await fetchUserData(true);

      // Notify parent component to refresh war data
      if (onParticipationComplete) {
        onParticipationComplete();
      }
    } catch (error) {
      console.error("Failed to switch sides:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6 mt-6">
      <h2 className="text-xl font-bold text-white mb-4">Participate in War</h2>

      {/* Revolution Side Display and Switch */}
      {war?.warType === 'revolution' && userSide && (
        <div className="mb-4 p-3 bg-gray-700 rounded-md">
          <div className="flex justify-between items-center mb-3">
            <span className="text-white font-medium">Your Side:</span>
            <span className={`font-bold ${userSide === 'attacker' ? 'text-red-400' : 'text-blue-400'}`}>
              {userSide === 'attacker' ? 'Attacker' : 'Defender'}
            </span>
          </div>
          <div className="text-center">
            {userSide === 'attacker' ? (
              <button
                onClick={() => handleSwitchSide('defender')}
                disabled={loading || currentEnergy <= 0}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Switching...' : 'Switch to Defender Side'}
              </button>
            ) : (
              <button
                onClick={() => handleSwitchSide('attacker')}
                disabled={loading || currentEnergy <= 0}
                className="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Switching...' : 'Switch to Attacker Side'}
              </button>
            )}
            <p className="text-gray-400 text-xs mt-2">
              Switching sides requires energy and will immediately change your allegiance
            </p>
          </div>
        </div>
      )}

      {/* Energy Display */}
      <div className="mb-4 p-3 bg-gray-700 rounded-md">
        <div className="flex justify-between items-center">
          <span className="text-white font-medium">Your Energy:</span>
          <span
            className={`font-bold ${
              currentEnergy > 0 ? "text-neonBlue" : "text-red-400"
            }`}
          >
            {currentEnergy}
          </span>
        </div>
      </div>

      <div className="space-y-6">
        {/* Manual Attack Button */}
        <div>
          <h3 className="text-lg font-medium text-white mb-2">Manual Attack</h3>
          <p className="text-gray-300 mb-4">
            Attack now using all your available energy.
          </p>
          <button
            onClick={handleManualAttack}
            disabled={loading || currentEnergy <= 0}
            className="w-full bg-red-600 hover:bg-red-700 font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            {loading
              ? "Processing..."
              : currentEnergy <= 0
              ? "No Energy"
              : "Attack Now"}
          </button>
        </div>

        {/* Auto Mode Section */}
        <div className="border-t border-gray-700 pt-6">
          <h3 className="text-lg font-medium text-white mb-2">
            Auto Mode {!isPremium && "(Premium Only)"}
          </h3>

          <div className="bg-gray-700 p-4 rounded-md text-sm text-gray-300 mb-4">
            <p className="mb-2">
              When auto mode is enabled, the system will automatically attack
              every 30 minutes using all your available energy.
            </p>
            {!isPremium && (
              <p className="text-yellow-400 mt-2">
                This feature requires a premium account. Upgrade to premium to
                enable auto mode.
              </p>
            )}
            {isPremium && isAutoModeActive && (
              <div className="mt-3 p-2 bg-green-600 rounded-md">
                <p className="text-white font-medium">
                  ✓ Auto Mode is currently active for this war
                </p>
              </div>
            )}
          </div>

          <div className="space-y-3">
            {/* Enable Auto Mode Button */}
            {isPremium && !isAutoModeActive && (
              <button
                onClick={handleEnableAutoMode}
                disabled={loading || currentEnergy <= 0}
                className="w-full bg-blue-600 hover:bg-blue-700 font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 text-white"
              >
                {loading
                  ? "Processing..."
                  : currentEnergy <= 0
                  ? "No Energy"
                  : "Enable Auto Mode"}
              </button>
            )}

            {/* Disable Auto Mode Button */}
            {isPremium && isAutoModeActive && (
              <button
                onClick={handleDisableAutoMode}
                disabled={loading}
                className="w-full bg-red-600 hover:bg-red-700 font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 text-white"
              >
                {loading ? "Processing..." : "Disable Auto Mode"}
              </button>
            )}

            {/* Show message when user doesn't have premium */}
            {!isPremium && (
              <div className="w-full bg-gray-600 font-medium py-3 px-4 rounded-md text-gray-400 text-center">
                Premium Required for Auto Mode
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarParticipateForm;
