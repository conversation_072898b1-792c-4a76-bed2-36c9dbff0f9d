import React from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Navbar from '../components/Navbar';
import { FaTimesCircle, FaArrowLeft, FaQuestionCircle } from 'react-icons/fa';

export default function PaymentCancelPage() {
  useAuthGuard();
  const [searchParams] = useSearchParams();

  // Get payment details from URL parameters
  const paymentType = searchParams.get('type') || 'subscription';
  const plan = searchParams.get('plan') || 'premium';
  const goldPackage = searchParams.get('package');

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <div className="flex justify-center mb-6">
            <FaTimesCircle className="text-red-400 text-6xl" />
          </div>

          <h1 className="text-3xl font-bold text-white mb-4">Payment Cancelled</h1>

          <p className="text-xl text-gray-300 mb-6">
            Your payment process was cancelled. No charges have been made.
          </p>

          <div className="bg-gray-700 p-6 rounded-lg mb-8 max-w-md mx-auto">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center justify-center">
              <FaQuestionCircle className="text-yellow-400 mr-2" />
              Common Reasons for Cancellation
            </h2>
            <ul className="text-left space-y-3">
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                <span className="text-gray-300">Payment method declined</span>
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                <span className="text-gray-300">Manually cancelled during checkout</span>
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                <span className="text-gray-300">Connection issues during payment</span>
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                <span className="text-gray-300">Insufficient funds</span>
              </li>
            </ul>
          </div>

          <div className="flex flex-col sm:flex-row justify-center gap-4 mt-8">
            <Link
              to="/shop"
              className="bg-neonBlue hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center"
            >
              <FaArrowLeft className="mr-2" /> Try Again
            </Link>

            <Link
              to="/home"
              className="bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center"
            >
              Return to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
