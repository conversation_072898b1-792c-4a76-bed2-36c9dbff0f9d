import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';

const DamageLeaderboard = ({ limit = 10 }) => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        setLoading(true);
        const data = await warService.getDamageLeaderboard(limit);
        setLeaderboard(data);
      } catch (error) {
        console.error('Failed to fetch damage leaderboard:', error);
        toast.error('Failed to load damage leaderboard');
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
  }, [limit]);

  const getSideColor = (side) => {
    switch (side) {
      case 'attacker':
        return 'text-red-400';
      case 'defender':
        return 'text-blue-400';
      case 'mixed':
        return 'text-purple-400';
      default:
        return 'text-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6 animate-pulse">
        <div className="h-6 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="h-64 bg-gray-700 rounded"></div>
      </div>
    );
  }

  if (leaderboard.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">Damage Leaderboard</h2>
        <p className="text-gray-400">No leaderboard data available</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">Damage Leaderboard</h2>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-600">
          <thead className="bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Rank
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Player
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Total Damage
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Wars
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Avg. Damage
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Highest Damage
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">
                Side
              </th>
            </tr>
          </thead>
          <tbody className="bg-gray-800 divide-y divide-gray-700">
            {leaderboard.map((player, index) => (
              <tr key={player.userId} className={index % 2 === 0 ? 'bg-gray-750' : 'bg-gray-800'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                  {index + 1}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <Link to={`/users/${player.userId}`} className="text-neonBlue hover:text-blue-400">
                    {player.username}
                  </Link>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {player.totalDamage.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {player.warCount}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {player.averageDamagePerWar.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {player.highestSingleWarDamage.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSideColor(player.side)}`}>
                    {player.side.charAt(0).toUpperCase() + player.side.slice(1)}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {leaderboard.length >= limit && (
        <div className="mt-6 text-center">
          <button 
            onClick={() => warService.getDamageLeaderboard(limit + 10).then(setLeaderboard)}
            className="inline-block px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Load More
          </button>
        </div>
      )}
    </div>
  );
};

export default DamageLeaderboard;
