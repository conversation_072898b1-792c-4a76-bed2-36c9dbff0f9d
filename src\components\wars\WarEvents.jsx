import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { toast } from 'react-toastify';

const WarEvents = ({ warId }) => {
  const [loading, setLoading] = useState(true);
  const [events, setEvents] = useState([]);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10
  });

  useEffect(() => {
    fetchEvents(1);
  }, [warId]);

  const fetchEvents = async (page = 1, limit = 10) => {
    setLoading(true);
    try {
      const data = await warService.getWarEvents(warId, page, limit);
      setEvents(data.events);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Failed to fetch war events:', error);
      toast.error('Failed to load war events');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= Math.ceil(pagination.total / pagination.limit)) {
      fetchEvents(newPage, pagination.limit);
    }
  };

  const getEventTypeIcon = (type) => {
    switch (type.toLowerCase()) {
      case 'battle':
        return '⚔️';
      case 'supply':
        return '📦';
      case 'morale':
        return '🎖️';
      case 'reinforcement':
        return '👥';
      case 'sabotage':
        return '💣';
      case 'diplomacy':
        return '🤝';
      default:
        return '📜';
    }
  };

  if (loading && events.length === 0) {
    return <div className="text-center py-8"><div className="loader">Loading events...</div></div>;
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">War Events</h2>
      
      {events.length > 0 ? (
        <div className="space-y-4">
          {events.map((event) => (
            <div key={event.id} className="bg-gray-700 p-4 rounded-md">
              <div className="flex items-start">
                <div className="text-2xl mr-3">{getEventTypeIcon(event.type)}</div>
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <h3 className="text-lg font-medium text-white">{event.type}</h3>
                    {event.moraleImpact !== 0 && (
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        event.moraleImpact > 0 
                          ? 'bg-green-900 text-green-300' 
                          : 'bg-red-900 text-red-300'
                      }`}>
                        Morale: {event.moraleImpact > 0 ? '+' : ''}{event.moraleImpact}
                      </span>
                    )}
                  </div>
                  
                  {/* Event details will vary based on the event type */}
                  {event.eventDetails && (
                    <div className="mt-2 text-gray-300">
                      {typeof event.eventDetails === 'string' 
                        ? event.eventDetails 
                        : Object.entries(event.eventDetails).map(([key, value]) => (
                            <div key={key} className="flex justify-between text-sm">
                              <span className="text-gray-400">{key}:</span>
                              <span>{value}</span>
                            </div>
                          ))
                      }
                    </div>
                  )}
                  
                  <div className="text-xs text-gray-400 mt-2">
                    {new Date(event.timestamp).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {/* Pagination */}
          {pagination.total > pagination.limit && (
            <div className="flex justify-center mt-6">
              <nav className="flex items-center">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                  className="px-3 py-1 rounded-md mr-2 bg-gray-700 text-white disabled:opacity-50"
                >
                  Previous
                </button>
                
                <div className="text-gray-300">
                  Page {pagination.page} of {Math.ceil(pagination.total / pagination.limit)}
                </div>
                
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
                  className="px-3 py-1 rounded-md ml-2 bg-gray-700 text-white disabled:opacity-50"
                >
                  Next
                </button>
              </nav>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-400">
          No events recorded for this war yet
        </div>
      )}
    </div>
  );
};

export default WarEvents;
