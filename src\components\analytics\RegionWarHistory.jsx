import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';

const RegionWarHistory = ({ regionId }) => {
  const [history, setHistory] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRegionHistory = async () => {
      if (!regionId) return;
      
      try {
        setLoading(true);
        const data = await warService.getRegionWarHistory(regionId);
        setHistory(data);
      } catch (error) {
        console.error('Failed to fetch region war history:', error);
        toast.error('Failed to load region war history');
      } finally {
        setLoading(false);
      }
    };

    fetchRegionHistory();
  }, [regionId]);

  if (!regionId) {
    return null;
  }

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6 animate-pulse">
        <div className="h-6 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="grid grid-cols-2 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-700 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!history) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <p className="text-gray-400">No war history available for this region</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">War History: {history.regionName}</h2>
      
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Wars as Attacker</h3>
            <span className="text-red-500 text-2xl">⚔️</span>
          </div>
          <p className="text-2xl font-bold text-white">{history.warsAsAttacker}</p>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Wars as Defender</h3>
            <span className="text-blue-500 text-2xl">🛡️</span>
          </div>
          <p className="text-2xl font-bold text-white">{history.warsAsDefender}</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Conquest Wars</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Won:</span>
              <span className="text-green-400">{history.conquestsWon}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Lost:</span>
              <span className="text-red-400">{history.conquestsLost}</span>
            </div>
            <div className="flex justify-between border-t border-gray-600 pt-2 mt-2">
              <span className="text-gray-400">Win Rate:</span>
              <span className="text-white">
                {history.conquestsWon + history.conquestsLost > 0
                  ? `${Math.round((history.conquestsWon / (history.conquestsWon + history.conquestsLost)) * 100)}%`
                  : 'N/A'}
              </span>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Resource Wars</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Won:</span>
              <span className="text-green-400">{history.resourceWarsWon}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Lost:</span>
              <span className="text-red-400">{history.resourceWarsLost}</span>
            </div>
            <div className="flex justify-between border-t border-gray-600 pt-2 mt-2">
              <span className="text-gray-400">Win Rate:</span>
              <span className="text-white">
                {history.resourceWarsWon + history.resourceWarsLost > 0
                  ? `${Math.round((history.resourceWarsWon / (history.resourceWarsWon + history.resourceWarsLost)) * 100)}%`
                  : 'N/A'}
              </span>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Revolution Wars</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Won:</span>
              <span className="text-green-400">{history.revolutionsWon}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Lost:</span>
              <span className="text-red-400">{history.revolutionsLost}</span>
            </div>
            <div className="flex justify-between border-t border-gray-600 pt-2 mt-2">
              <span className="text-gray-400">Win Rate:</span>
              <span className="text-white">
                {history.revolutionsWon + history.revolutionsLost > 0
                  ? `${Math.round((history.revolutionsWon / (history.revolutionsWon + history.revolutionsLost)) * 100)}%`
                  : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-medium text-white">Overall Performance</h3>
        </div>
        <div className="bg-gray-700 p-4 rounded-md">
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-400">Total Wars</span>
                <span className="text-sm text-white">
                  {history.warsAsAttacker + history.warsAsDefender}
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-400">Total Victories</span>
                <span className="text-sm text-white">
                  {history.conquestsWon + history.resourceWarsWon + history.revolutionsWon}
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-400">Total Defeats</span>
                <span className="text-sm text-white">
                  {history.conquestsLost + history.resourceWarsLost + history.revolutionsLost}
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-400">Overall Win Rate</span>
                <span className="text-sm text-white">
                  {(() => {
                    const wins = history.conquestsWon + history.resourceWarsWon + history.revolutionsWon;
                    const losses = history.conquestsLost + history.resourceWarsLost + history.revolutionsLost;
                    const total = wins + losses;
                    
                    return total > 0 ? `${Math.round((wins / total) * 100)}%` : 'N/A';
                  })()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-6 text-center">
        <Link to="/wars" className="inline-block px-4 py-2 bg-neonBlue text-white rounded-md hover:bg-blue-600 transition-colors">
          View All Wars
        </Link>
      </div>
    </div>
  );
};

export default RegionWarHistory;
