export enum FactoryType {
  GOLD = 'GOLD',
  MONEY = 'MONEY'
}

export interface Factory {
  id: number;
  name: string;
  type: FactoryType;
  regionId: string;
  ownerId: number;
  wage: number;
  maxWorkers: number;
  currentWorkers: number;
  energyCost: number;
  resourcePerWork: number;
  createdAt: string;
  updatedAt: string;
  owner?: {
    id: number;
    username: string;
  };
  region?: {
    id: string;
    name: string;
  };
}

export interface WorkSession {
  id: number;
  factoryId: number;
  workerId: number;
  energySpent: number;
  wageEarned: number;
  resourceEarned: number;
  createdAt: string;
  updatedAt: string;
  factory?: Factory;
  worker?: {
    id: number;
    username: string;
  };
} 