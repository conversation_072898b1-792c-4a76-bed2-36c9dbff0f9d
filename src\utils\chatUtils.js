import DOMPurify from 'dompurify';
import { CHAT_CONFIG } from '../config/chat.config.js';

/**
 * Chat utility functions
 * Helper functions for chat-related operations
 */

/**
 * Sanitize message content to prevent XSS attacks
 * @param {string} content - Raw message content
 * @returns {string} Sanitized content
 */
export const sanitizeMessageContent = (content) => {
  if (!CHAT_CONFIG.SECURITY.SANITIZE_HTML) {
    return content;
  }

  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: [], // No HTML tags allowed
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
  });
};

/**
 * Format timestamp for display
 * @param {string|Date} timestamp - Timestamp to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted timestamp
 */
export const formatTimestamp = (timestamp, options = {}) => {
  const {
    includeSeconds = true,
    includeDate = false,
    relative = false,
  } = options;

  const date = new Date(timestamp);
  const now = new Date();

  if (relative) {
    const diffInMs = now - date;
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 1) return 'now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays < 7) return `${diffInDays}d ago`;
  }

  const timeOptions = {
    hour: '2-digit',
    minute: '2-digit',
    ...(includeSeconds && { second: '2-digit' }),
  };

  if (includeDate) {
    return date.toLocaleString([], {
      ...timeOptions,
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  return date.toLocaleTimeString([], timeOptions);
};

/**
 * Get display name for a chat
 * @param {Object} chat - Chat object
 * @param {Object} currentUser - Current user object
 * @returns {string} Display name
 */
export const getChatDisplayName = (chat, currentUser) => {
  if (chat.name) return chat.name;

  if (chat.type === 'direct') {
    const otherUser = chat.participants.find(p => p.id !== currentUser?.id);
    return otherUser?.username || 'Unknown User';
  }

  return 'Group Chat';
};

/**
 * Get chat description/subtitle
 * @param {Object} chat - Chat object
 * @param {Object} currentUser - Current user object
 * @returns {string} Chat description
 */
export const getChatDescription = (chat, currentUser) => {
  if (chat.type === 'group') {
    return `${chat.participants.length} participants`;
  }

  const otherUser = chat.participants.find(p => p.id !== currentUser?.id);
  return otherUser ? `Level ${otherUser.level}` : 'Direct message';
};

/**
 * Get last message preview
 * @param {Object} chat - Chat object
 * @param {Object} currentUser - Current user object
 * @param {number} maxLength - Maximum preview length
 * @returns {string} Message preview
 */
export const getLastMessagePreview = (chat, currentUser, maxLength = 50) => {
  if (!chat.lastMessage) return 'No messages yet';

  const { content, sender, type } = chat.lastMessage;

  if (type === 'system') {
    return content.length > maxLength 
      ? `${content.substring(0, maxLength)}...`
      : content;
  }

  const isOwnMessage = sender.id === currentUser?.id;
  const senderName = isOwnMessage ? 'You' : sender.username;
  
  const truncatedContent = content.length > maxLength 
    ? `${content.substring(0, maxLength)}...`
    : content;

  return `${senderName}: ${truncatedContent}`;
};

/**
 * Check if two messages should be grouped together
 * @param {Object} currentMessage - Current message
 * @param {Object} previousMessage - Previous message
 * @param {number} timeThreshold - Time threshold in minutes (default: 5)
 * @returns {boolean} Whether messages should be grouped
 */
export const shouldGroupMessages = (currentMessage, previousMessage, timeThreshold = 5) => {
  if (!previousMessage) return false;

  const timeDiff = new Date(currentMessage.createdAt) - new Date(previousMessage.createdAt);
  const thresholdMs = timeThreshold * 60 * 1000;

  return (
    currentMessage.sender.id === previousMessage.sender.id &&
    timeDiff < thresholdMs
  );
};

/**
 * Check if a date separator should be shown
 * @param {Object} currentMessage - Current message
 * @param {Object} previousMessage - Previous message
 * @returns {boolean} Whether to show date separator
 */
export const shouldShowDateSeparator = (currentMessage, previousMessage) => {
  if (!previousMessage) return true;

  const currentDate = new Date(currentMessage.createdAt).toDateString();
  const previousDate = new Date(previousMessage.createdAt).toDateString();

  return currentDate !== previousDate;
};

/**
 * Format date for date separator
 * @param {string|Date} timestamp - Timestamp
 * @returns {string} Formatted date
 */
export const formatDateSeparator = (timestamp) => {
  const date = new Date(timestamp);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (date.toDateString() === today.toDateString()) {
    return 'Today';
  } else if (date.toDateString() === yesterday.toDateString()) {
    return 'Yesterday';
  } else {
    return date.toLocaleDateString([], {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }
};

/**
 * Generate typing indicator text
 * @param {Array} typingUsers - Array of typing users
 * @param {Object} currentUser - Current user object
 * @returns {string} Typing indicator text
 */
export const getTypingIndicatorText = (typingUsers, currentUser) => {
  // Filter out current user
  const otherTypingUsers = typingUsers.filter(user => user.userId !== currentUser?.id);
  
  if (otherTypingUsers.length === 0) return '';
  
  if (otherTypingUsers.length === 1) {
    return `${otherTypingUsers[0].username} is typing...`;
  }
  
  if (otherTypingUsers.length === 2) {
    return `${otherTypingUsers[0].username} and ${otherTypingUsers[1].username} are typing...`;
  }
  
  return `${otherTypingUsers.length} people are typing...`;
};

/**
 * Validate message content
 * @param {string} content - Message content
 * @returns {Object} Validation result
 */
export const validateMessageContent = (content) => {
  if (!content || content.trim().length === 0) {
    return {
      valid: false,
      error: CHAT_CONFIG.ERRORS.MESSAGE_EMPTY,
    };
  }

  if (content.length > CHAT_CONFIG.MESSAGES.MAX_LENGTH) {
    return {
      valid: false,
      error: CHAT_CONFIG.ERRORS.MESSAGE_TOO_LONG,
    };
  }

  return { valid: true };
};

/**
 * Generate unique chat ID for direct chats
 * @param {number} userId1 - First user ID
 * @param {number} userId2 - Second user ID
 * @returns {string} Unique chat identifier
 */
export const generateDirectChatId = (userId1, userId2) => {
  const sortedIds = [userId1, userId2].sort((a, b) => a - b);
  return `direct_${sortedIds[0]}_${sortedIds[1]}`;
};

/**
 * Check if user has permission to perform action in chat
 * @param {Object} chat - Chat object
 * @param {Object} user - User object
 * @param {string} action - Action to check ('send', 'read', 'manage')
 * @returns {boolean} Whether user has permission
 */
export const hasPermission = (chat, user, action) => {
  if (!chat || !user) return false;

  // Check if user is a participant
  const isParticipant = chat.participants.some(p => p.id === user.id);
  if (!isParticipant) return false;

  switch (action) {
    case 'send':
    case 'read':
      return true; // All participants can send and read
    case 'manage':
      // For now, all participants can manage (future: add admin roles)
      return true;
    default:
      return false;
  }
};

/**
 * Sort chats by last activity
 * @param {Array} chats - Array of chat objects
 * @returns {Array} Sorted chats
 */
export const sortChatsByActivity = (chats) => {
  return [...chats].sort((a, b) => {
    const aTime = a.lastMessage?.createdAt || a.updatedAt || a.createdAt;
    const bTime = b.lastMessage?.createdAt || b.updatedAt || b.createdAt;
    return new Date(bTime) - new Date(aTime);
  });
};

/**
 * Filter chats by search query
 * @param {Array} chats - Array of chat objects
 * @param {string} query - Search query
 * @param {Object} currentUser - Current user object
 * @returns {Array} Filtered chats
 */
export const filterChatsByQuery = (chats, query, currentUser) => {
  if (!query.trim()) return chats;

  const searchTerm = query.toLowerCase();

  return chats.filter(chat => {
    // Search in chat name
    if (chat.name && chat.name.toLowerCase().includes(searchTerm)) {
      return true;
    }

    // Search in participant names
    const participantMatch = chat.participants.some(participant => 
      participant.id !== currentUser?.id &&
      participant.username.toLowerCase().includes(searchTerm)
    );

    if (participantMatch) return true;

    // Search in last message content
    if (chat.lastMessage && 
        chat.lastMessage.content.toLowerCase().includes(searchTerm)) {
      return true;
    }

    return false;
  });
};

/**
 * Calculate unread count for all chats
 * @param {Array} chats - Array of chat objects
 * @returns {number} Total unread count
 */
export const calculateTotalUnreadCount = (chats) => {
  return chats.reduce((total, chat) => total + (chat.unreadCount || 0), 0);
};

/**
 * Debounce function for performance optimization
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttle function for performance optimization
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Check if browser supports notifications
 * @returns {boolean} Whether notifications are supported
 */
export const supportsNotifications = () => {
  return 'Notification' in window;
};

/**
 * Check notification permission status
 * @returns {string} Permission status ('granted', 'denied', 'default')
 */
export const getNotificationPermission = () => {
  if (!supportsNotifications()) return 'denied';
  return Notification.permission;
};

/**
 * Show desktop notification for new message
 * @param {Object} message - Message object
 * @param {Object} chat - Chat object
 * @param {Object} currentUser - Current user object
 */
export const showMessageNotification = (message, chat, currentUser) => {
  if (!supportsNotifications() || getNotificationPermission() !== 'granted') {
    return;
  }

  // Don't show notification for own messages
  if (message.sender.id === currentUser?.id) return;

  const title = chat.type === 'direct' 
    ? message.sender.username
    : `${message.sender.username} in ${getChatDisplayName(chat, currentUser)}`;

  const notification = new Notification(title, {
    body: message.content,
    icon: '/wn-icon.png',
    tag: `chat-${chat.id}`,
    requireInteraction: false,
  });

  // Auto-close notification after timeout
  setTimeout(() => {
    notification.close();
  }, CHAT_CONFIG.NOTIFICATIONS.NOTIFICATION_TIMEOUT);

  return notification;
};

export default {
  sanitizeMessageContent,
  formatTimestamp,
  getChatDisplayName,
  getChatDescription,
  getLastMessagePreview,
  shouldGroupMessages,
  shouldShowDateSeparator,
  formatDateSeparator,
  getTypingIndicatorText,
  validateMessageContent,
  generateDirectChatId,
  hasPermission,
  sortChatsByActivity,
  filterChatsByQuery,
  calculateTotalUnreadCount,
  debounce,
  throttle,
  supportsNotifications,
  getNotificationPermission,
  showMessageNotification,
};
