import { api } from './api';

export interface InitiateTravelDto {
  destinationRegionId: string;
}

export interface TravelTimeEstimateDto {
  originRegionId: string;
  destinationRegionId: string;
}

export interface RequestTravelPermissionDto {
  destinationRegionId: string;
  reason?: string;
}

export interface RespondToPermissionRequestDto {
  approve: boolean;
  message?: string;
}

export interface Travel {
  id: string;
  sourceRegion: {
    id: string;
    name: string;
    [key: string]: any;
  };
  destinationRegion: {
    id: string;
    name: string;
    [key: string]: any;
  };
  startTime: string;
  endTime: string;
  status: 'in_progress' | 'completed' | 'cancelled';
  distance: number;
  travelTime: number;
  seaCrossing: boolean;
  sameState: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TravelPermission {
  id: string;
  userId: number;
  destinationRegionId: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  reason?: string;
  responseMessage?: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: number;
    username: string;
  };
  destinationRegion?: {
    id: string;
    name: string;
    state?: {
      id: string;
      name: string;
    };
  };
}

export const travelService = {
  // Initiate travel to another region
  initiateTravel: async (initiateTravelDto: InitiateTravelDto): Promise<Travel> => {
    const response = await api.post('/travel/initiate', initiateTravelDto);
    return response.data;
  },

  // Get current travel status
  getCurrentTravel: async (): Promise<Travel | null> => {
    const response = await api.get('/travel/current');
    return response.data;
  },

  // Cancel an in-progress travel
  cancelTravel: async (travelId: string): Promise<Travel> => {
    const response = await api.post(`/travel/cancel/${travelId}`);
    return response.data;
  },

  // Get estimated travel time between regions
  getTravelTimeEstimate: async (travelTimeEstimateDto: TravelTimeEstimateDto): Promise<{
    distance: number;
    travelTime: number;
    seaCrossing: boolean;
    sameState: boolean;
  }> => {
    const response = await api.post('/travel/time-estimate', travelTimeEstimateDto);
    return response.data;
  },

  // Request permission to travel to a region
  requestTravelPermission: async (requestTravelPermissionDto: RequestTravelPermissionDto): Promise<TravelPermission> => {
    const response = await api.post('/travel/request-permission', requestTravelPermissionDto);
    return response.data;
  },

  // Respond to a travel permission request (for state leaders)
  respondToPermissionRequest: async (
    requestId: string,
    respondToPermissionRequestDto: RespondToPermissionRequestDto
  ): Promise<TravelPermission> => {
    const response = await api.patch(`/travel/permission-requests/${requestId}`, respondToPermissionRequestDto);
    return response.data;
  },

  // Get all permission requests for states led by the user
  getStatePermissionRequests: async (): Promise<TravelPermission[]> => {
    const response = await api.get('/travel/permission-requests/state');
    return response.data;
  },

  // Get all of the user's permission requests
  getUserPermissionRequests: async (): Promise<TravelPermission[]> => {
    const response = await api.get('/travel/permission-requests/user');
    return response.data;
  }
};

export default travelService;
