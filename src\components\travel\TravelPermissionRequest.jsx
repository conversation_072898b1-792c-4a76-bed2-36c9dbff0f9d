import React, { useState } from "react";
import { travelService } from "../../services/api/travel.service";
import { showErrorToast } from "../../utils/showErrorToast";
import { showSuccessToast } from "../../utils/showSuccessToast";
import { FaLock, FaCheck } from "react-icons/fa";

const TravelPermissionRequest = ({ regionId, regionName, onPermissionRequested }) => {
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const handleRequestPermission = async (e) => {
    e.preventDefault();
    
    try {
      setIsSubmitting(true);
      await travelService.requestTravelPermission({
        destinationRegionId: regionId,
        reason: reason.trim() || undefined
      });
      
      showSuccessToast("Permission request submitted successfully");
      setReason("");
      setShowForm(false);
      
      if (onPermissionRequested) {
        onPermissionRequested();
      }
    } catch (err) {
      console.error("Error requesting travel permission:", err);
      showErrorToast(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-yellow-900/30 rounded-lg p-4 mb-4">
      <div className="flex items-center mb-3">
        <FaLock className="text-yellow-500 mr-2" />
        <h3 className="text-lg font-semibold text-white">
          Permission Required
        </h3>
      </div>
      
      <p className="text-gray-300 mb-4">
        You need permission from the leader of this region's state to travel to {regionName}.
      </p>
      
      {!showForm ? (
        <button
          onClick={() => setShowForm(true)}
          className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md"
        >
          Request Permission
        </button>
      ) : (
        <form onSubmit={handleRequestPermission} className="space-y-3">
          <div>
            <label htmlFor="reason" className="block text-gray-300 mb-1">
              Reason for Visit (Optional)
            </label>
            <textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full bg-gray-800 text-white border border-gray-700 rounded-md p-2"
              rows="3"
              placeholder="Explain why you want to visit this region..."
            ></textarea>
          </div>
          
          <div className="flex space-x-3">
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md flex items-center disabled:bg-gray-600 disabled:text-gray-400"
            >
              <FaCheck className="mr-2" />
              {isSubmitting ? "Submitting..." : "Submit Request"}
            </button>
            
            <button
              type="button"
              onClick={() => setShowForm(false)}
              className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md"
            >
              Cancel
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default TravelPermissionRequest;
