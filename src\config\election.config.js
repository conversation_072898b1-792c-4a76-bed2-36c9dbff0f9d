/**
 * Election System Configuration
 * 
 * This file contains all configurable values for the election system.
 * Modify these values to customize election behavior without changing code.
 */

export const ELECTION_CONFIG = {
  // Election Duration Settings
  DEFAULT_DURATION_HOURS: parseInt(import.meta.env.VITE_ELECTION_DURATION_HOURS) || 1, // 2 days
  MIN_DURATION_HOURS: 24, // Minimum 1 day
  MAX_DURATION_HOURS: 24, // Maximum 1 week

  // UI Update Intervals
  COUNTDOWN_UPDATE_INTERVAL: 1000, // 1 second for countdown timer
  PROGRESS_CALCULATION_DURATION: 1 * 24 * 60 * 60, // 2 days in seconds for progress bar

  // Pagination Settings
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: parseInt(import.meta.env.VITE_ELECTION_HISTORY_LIMIT) || 10,
    MAX_LIMIT: 50,
    MIN_LIMIT: 5
  },

  // Voting Rules
  WINNER_THRESHOLD_PERCENTAGE: parseFloat(import.meta.env.VITE_WINNER_THRESHOLD) || 50, // Simple majority
  ALLOW_MULTIPLE_VOTES: false, // Users can only vote once
  REQUIRE_STATE_MEMBERSHIP: true, // Only state members can vote

  // UI Display Settings
  CANDIDATE_PREVIEW_LIMIT: parseInt(import.meta.env.VITE_CANDIDATE_PREVIEW_LIMIT) || 5,
  MANIFESTO_PREVIEW_LENGTH: parseInt(import.meta.env.VITE_MANIFESTO_PREVIEW_LENGTH) || 100,
  MAX_CANDIDATES_PER_ELECTION: 20,

  // Feature Flags
  FEATURES: {
    REAL_TIME_UPDATES: import.meta.env.VITE_ENABLE_REAL_TIME_UPDATES !== 'false',
    ELECTION_NOTIFICATIONS: import.meta.env.VITE_ENABLE_NOTIFICATIONS !== 'false',
    CANDIDATE_REGISTRATION: import.meta.env.VITE_ENABLE_CANDIDATE_REGISTRATION === 'true',
    ELECTION_STATISTICS: import.meta.env.VITE_ENABLE_STATISTICS !== 'false',
    EXPORT_FUNCTIONALITY: import.meta.env.VITE_ENABLE_EXPORT === 'true'
  },

  // Security Settings
  SECURITY: {
    ENABLE_RATE_LIMITING: true,
    MAX_VOTES_PER_MINUTE: 1,
    REQUIRE_CONFIRMATION: true,
    LOG_VOTING_ATTEMPTS: import.meta.env.NODE_ENV === 'production'
  },

  // Error Messages
  MESSAGES: {
    NO_ACTIVE_ELECTION: 'No active election found for this state.',
    ALREADY_VOTED: 'You have already voted in this election.',
    NOT_ELIGIBLE: 'You are not eligible to vote in this election.',
    ELECTION_ENDED: 'This election has ended.',
    NETWORK_ERROR: 'Network error - please check your connection.',
    VOTE_SUCCESS: 'Your vote has been successfully submitted!',
    VOTE_ERROR: 'Failed to submit vote. Please try again.'
  },

  // Status Colors (CSS classes)
  STATUS_COLORS: {
    active: 'bg-green-900/30 text-green-400',
    completed: 'bg-blue-900/30 text-blue-400',
    pending: 'bg-yellow-900/30 text-yellow-400',
    cancelled: 'bg-red-900/30 text-red-400',
    default: 'bg-gray-600 text-gray-300'
  },

  // Development Settings (only used in development)
  DEV: {
    ENABLE_DEBUG_LOGS: import.meta.env.NODE_ENV === 'development',
    MOCK_DATA: import.meta.env.VITE_USE_MOCK_DATA === 'true',
    SKIP_AUTH: import.meta.env.VITE_SKIP_AUTH === 'true'
  }
};