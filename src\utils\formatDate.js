/**
 * Formats a date with hours, minutes, and seconds
 * @param {Date|string} date - The date to format
 * @returns {string} - The formatted date string
 */
export function formatDate(date) {
  if (!date) return 'Unknown';
  
  return new Date(date).toLocaleString(undefined, {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}
