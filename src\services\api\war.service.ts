import { api } from './api';
import { War, CreateWarDto, ParticipateInWarDto } from '../../types/war';
import {
  WarPreparationDto,
  WarPreparationResponse,
  WarSuppliesResponse,
  AddWarSuppliesDto,
  WarMoraleResponse,
  WarCostsResponse,
  BattleEventResponse,
  WarEventsResponse
} from '../../types/warPreparation';
import {
  WarStatistics,
  UserWarStatistics,
  WarTimelineEvent,
  RegionWarHistory,
  DamageLeaderboard,
  EfficiencyMetrics,
  RegionalPerformance,
  WarTrends
} from '../../types/warAnalytics';

export const warService = {
  // Get all wars
  getAllWars: async (): Promise<War[]> => {
    const response = await api.get('/wars');
    return response.data;
  },

  // Get all active wars
  getActiveWars: async (): Promise<War[]> => {
    const response = await api.get('/wars/active');
    return response.data;
  },

  // Get wars involving the current user
  getMyWars: async (): Promise<War[]> => {
    const response = await api.get('/wars/my-wars');
    return response.data;
  },

  // Get a single war by ID
  getWar: async (id: string): Promise<War> => {
    const response = await api.get(`/wars/${id}`);
    return response.data;
  },

  // Declare a new war
  declareWar: async (warData: CreateWarDto): Promise<War> => {
    const response = await api.post('/wars/declare', warData);
    return response.data;
  },

  // Join a war as attacker or defender
  joinWar: async (warId: string, isAttacker: boolean): Promise<War> => {
    const response = await api.post(`/wars/${warId}/join`, { isAttacker });
    return response.data;
  },

  // Participate in a war with energy
  participateInWar: async (warId: string, participateData: ParticipateInWarDto): Promise<War> => {
    const response = await api.post(`/wars/${warId}/participate`, participateData);
    return response.data;
  },

  // Prepare for war
  prepareForWar: async (warId: string, userId: string, preparationData: WarPreparationDto): Promise<WarPreparationResponse> => {
    const response = await api.post(`/wars/${warId}/prepare`, preparationData);
    return response.data;
  },

  // Get war supplies
  getWarSupplies: async (warId: string): Promise<WarSuppliesResponse> => {
    const response = await api.get(`/wars/${warId}/supplies`);
    return response.data;
  },

  // Add war supplies
  addWarSupplies: async (warId: string, userId: string, suppliesData: AddWarSuppliesDto): Promise<WarSuppliesResponse> => {
    const response = await api.post(`/wars/${warId}/supplies`, suppliesData);
    return response.data;
  },

  // Get war morale
  getWarMorale: async (warId: string): Promise<WarMoraleResponse> => {
    const response = await api.get(`/wars/${warId}/morale`);
    return response.data;
  },

  // Get war costs
  getWarCosts: async (warId: string): Promise<WarCostsResponse> => {
    const response = await api.get(`/wars/${warId}/costs`);
    return response.data;
  },

  // Get war events
  getWarEvents: async (warId: string, page: number = 1, limit: number = 10): Promise<WarEventsResponse> => {
    const response = await api.get(`/wars/${warId}/events`, {
      params: { page, limit }
    });
    return response.data;
  },

  // Analytics endpoints

  // Get global war statistics
  getGlobalWarStats: async (): Promise<WarStatistics> => {
    const response = await api.get('/wars/analytics/global');
    return response.data;
  },

  // Get user war statistics
  getUserWarStats: async (): Promise<UserWarStatistics> => {
    const response = await api.get('/wars/analytics/user');
    return response.data;
  },

  // Get war timeline events
  getWarTimeline: async (limit: number = 10): Promise<WarTimelineEvent[]> => {
    const response = await api.get('/wars/analytics/timeline', {
      params: { limit }
    });
    return response.data;
  },

  // Get region war history
  getRegionWarHistory: async (regionId: string): Promise<RegionWarHistory> => {
    const response = await api.get(`/wars/analytics/region/${regionId}`);
    return response.data;
  },

  // Advanced analytics endpoints

  // Get damage leaderboard
  getDamageLeaderboard: async (limit: number = 10): Promise<DamageLeaderboard[]> => {
    const response = await api.get('/wars/analytics/advanced/damage-leaderboard', {
      params: { limit }
    });
    return response.data;
  },

  // Get efficiency metrics
  getEfficiencyMetrics: async (limit: number = 10): Promise<EfficiencyMetrics[]> => {
    const response = await api.get('/wars/analytics/advanced/efficiency-metrics', {
      params: { limit }
    });
    return response.data;
  },

  // Get regional performance
  getRegionalPerformance: async (regionId: string): Promise<RegionalPerformance> => {
    const response = await api.get(`/wars/analytics/advanced/regional-performance/${regionId}`);
    return response.data;
  },

  // Get war trends
  getWarTrends: async (): Promise<WarTrends[]> => {
    const response = await api.get('/wars/analytics/advanced/trends');
    return response.data;
  },

  // Stop auto attack mode for a war
  stopAutoAttack: async (warId: string): Promise<{ message: string }> => {
    const response = await api.post(`/wars/${warId}/stop-auto-attack`);
    return response.data;
  },

  // Get user's current side in a revolution war
  getUserSideInRevolution: async (warId: string): Promise<{ side: 'attacker' | 'defender' | null }> => {
    const response = await api.get(`/wars/${warId}/user-side`);
    return response.data;
  }
};