export function calculateTrainingCost({
  perkLevel,
  duration, // in seconds
}) {
  const newLevel = perkLevel + 1;
  const hours = duration / 3600;

  // A small base cost that grows with level
  // plus a time-scaling part
  const baseGoldCost = 2 * newLevel;     // example: 2 gold per level
  const baseMoneyCost = 200 * newLevel;  // example: 200 $ per level

  const goldPerHour = 0.0735 * Math.pow(newLevel, 1.2);
  const moneyPerHour = 273.5 * Math.pow(newLevel, 1.2);

  let totalCostGold = baseGoldCost + goldPerHour * hours;
  let totalCostMoney = baseMoneyCost + moneyPerHour * hours;

  return {
    gold: Math.round(totalCostGold),
    money: Math.round(totalCostMoney),
  };
}
