import React, { useState } from 'react';
import { stripeService } from '../../services/api/stripe.service';
import { showErrorToast } from '../../utils/showErrorToast';
import { PremiumPlan } from '../../types/premium';

export default function PremiumSubscription() {
  const [loading, setLoading] = useState(false);

  const handleSubscribe = async () => {
    setLoading(true);
    try {
      // Create success and cancel URLs with query parameters
      const successUrl = `/payment/success?type=subscription&plan=${PremiumPlan.MONTHLY}`;
      const cancelUrl = `/payment/cancel?type=subscription&plan=${PremiumPlan.MONTHLY}`;

      const { url } = await stripeService.createSubscriptionSession(
        PremiumPlan.MONTHLY,
        successUrl,
        cancelUrl
      );
      // Redirect to the Stripe checkout page
      window.location.href = url;
    } catch (error) {
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-gray-800 p-6 rounded-lg">
      <h2 className="text-2xl font-bold text-white mb-4">Premium Subscription</h2>
      <div className="space-y-4">
        <div className="text-gray-300">
          <h3 className="text-xl font-semibold text-neonBlue">Benefits:</h3>
          <ul className="list-disc list-inside mt-2">
            <li>50% faster training times</li>
            <li>Auto-mode in wars</li>
            <li>Premium badge</li>
            <li>Additional features...</li>
          </ul>
        </div>
        <button
          onClick={handleSubscribe}
          disabled={loading}
          className="w-full bg-neonBlue hover:bg-blue-700 text-white py-3 rounded font-medium"
        >
          {loading ? 'Processing...' : 'Subscribe to Premium'}
        </button>
      </div>
    </div>
  );
}