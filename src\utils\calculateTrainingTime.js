export function calculateTrainingTime({
    perkLevel,
    eduResidency,
    currency = "gold",
    isPremium = false,
  }) {
    let EDU_INDEX_RESIDENCY = eduResidency;
  
    if (EDU_INDEX_RESIDENCY === 11) {
      EDU_INDEX_RESIDENCY = 20;
    }
  
    const base = 1 - EDU_INDEX_RESIDENCY / 50;
    let T = base * Math.pow(perkLevel + 1, 2) * (currency === "gold" ? 0.72 : 9.6);
  
    if (isPremium) T /= 2;
    if (perkLevel + 1 <= 100) T /= 2;
    if (perkLevel + 1 <= 50) T /= 2;
  
    return Math.round(T);
  }
  