import React, { useState } from 'react';
import { stateService } from '../../services/api/state.service';
import { showErrorToast } from '../../utils/showErrorToast';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { Link } from 'react-router-dom';
import StateForm from './StateForm';

const CreateStateModal = ({ isOpen, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    flagUrl: '',
    includeLeaderRegion: true
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      
      const result = await stateService.createState(formData);
      showSuccessToast('State created successfully!');
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      // Reset form and close modal
      setFormData({
        name: '',
        description: '',
        flagUrl: '',
        includeLeaderRegion: true
      });
      onClose();
    } catch (err) {
      console.error('Error creating state:', err);
      showErrorToast(err);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-neonBlue">Create a New State</h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <StateForm
          formData={formData}
          onChange={handleChange}
          onSubmit={handleSubmit}
          loading={loading}
          onClose={onClose}
          showRequirements={true}
          className="space-y-4"
        />

      </div>
    </div>
  );
};

export default CreateStateModal; 