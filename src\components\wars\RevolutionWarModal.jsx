import React, { useState } from 'react';
import { warService } from '../../services/api/war.service';
import { WarType, WarTarget } from '../../types/war.ts';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { showErrorToast } from '../../utils/showErrorToast';
import { FaFistRaised, FaTimes, FaCoins, FaUsers, FaClock } from 'react-icons/fa';

const RevolutionWarModal = ({ isOpen, onClose, region, userData, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [declaration, setDeclaration] = useState('');

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);

      // Validate requirements one more time
      if (!userData || userData.level < 5) {
        throw new Error('You must be level 5 or higher to start a revolution');
      }

      if (!userData || userData.gold < 500) {
        throw new Error('You need 500 gold to start a revolution');
      }

      if (!region?.users?.some(user => user.id === userData.id)) {
        throw new Error('You must be a citizen of this region to start a revolution');
      }

      // Check revolution cooldown
      if (region.lastRevolution) {
        const lastRevolutionDate = new Date(region.lastRevolution);
        const fourDaysAgo = new Date();
        fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
        if (lastRevolutionDate > fourDaysAgo) {
          throw new Error('This region is still in revolution cooldown period');
        }
      }

      const warData = {
        warType: WarType.REVOLUTION,
        warTarget: WarTarget.REVOLUTION,
        declaration: declaration.trim(),
        attackerRegionId: region.id,
        defenderRegionId: region.id, // Revolution is within the same region
      };

      const createdWar = await warService.declareWar(warData);
      
      showSuccessToast(`Revolution declared in ${region.name}!`);
      
      if (onSuccess) {
        onSuccess(createdWar);
      }
    } catch (err) {
      console.error('Error declaring revolution:', err);
      showErrorToast(err.response?.data?.message || err.message || 'Failed to declare revolution');
    } finally {
      setLoading(false);
    }
  };

  const canStartRevolution = () => {
    if (!userData || !region) return false;
    
    // Check if user is a citizen of this region
    const isUserCitizen = region.users?.some(user => user.id === userData.id);
    if (!isUserCitizen) return false;
    
    // Check user level (must be 5+)
    if (userData.level < 5) return false;
    
    // Check user has enough gold (500)
    if (userData.gold < 500) return false;
    
    // Check revolution cooldown (4 days)
    if (region.lastRevolution) {
      const lastRevolutionDate = new Date(region.lastRevolution);
      const fourDaysAgo = new Date();
      fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
      if (lastRevolutionDate > fourDaysAgo) return false;
    }
    
    return true;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div className="flex items-center">
            <FaFistRaised className="text-red-400 text-2xl mr-3" />
            <h2 className="text-2xl font-bold text-white">Start Revolution</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FaTimes className="text-xl" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Region Info */}
          <div className="bg-gray-700/30 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-white mb-3">Revolution Target</h3>
            <div className="flex items-center gap-4">
              {region?.imageUrl && (
                <img
                  src={region.imageUrl}
                  alt={region.name}
                  className="w-12 h-12 object-cover rounded-lg border-2 border-gray-600"
                />
              )}
              <div>
                <h4 className="text-white font-medium">{region?.name}</h4>
                {region?.state && (
                  <p className="text-gray-400 text-sm">
                    Currently part of {region.state.name}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Requirements */}
          <div className="bg-gray-700/30 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-white mb-3">Requirements</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FaCoins className="text-yellow-400 mr-2" />
                  <span className="text-gray-300">Cost</span>
                </div>
                <span className={`font-medium ${userData?.gold >= 500 ? 'text-green-400' : 'text-red-400'}`}>
                  500 Gold {userData?.gold >= 500 ? '✓' : '✗'}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FaUsers className="text-blue-400 mr-2" />
                  <span className="text-gray-300">Level Requirement</span>
                </div>
                <span className={`font-medium ${userData?.level >= 5 ? 'text-green-400' : 'text-red-400'}`}>
                  Level 5+ {userData?.level >= 5 ? '✓' : '✗'}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FaUsers className="text-purple-400 mr-2" />
                  <span className="text-gray-300">Citizenship</span>
                </div>
                <span className={`font-medium ${region?.users?.some(user => user.id === userData?.id) ? 'text-green-400' : 'text-red-400'}`}>
                  Region Citizen {region?.users?.some(user => user.id === userData?.id) ? '✓' : '✗'}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FaClock className="text-orange-400 mr-2" />
                  <span className="text-gray-300">Cooldown</span>
                </div>
                <span className={`font-medium ${!region?.lastRevolution || (() => {
                  const lastRevolutionDate = new Date(region.lastRevolution);
                  const fourDaysAgo = new Date();
                  fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
                  return lastRevolutionDate <= fourDaysAgo;
                })() ? 'text-green-400' : 'text-red-400'}`}>
                  Available {!region?.lastRevolution || (() => {
                    const lastRevolutionDate = new Date(region.lastRevolution);
                    const fourDaysAgo = new Date();
                    fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
                    return lastRevolutionDate <= fourDaysAgo;
                  })() ? '✓' : '✗'}
                </span>
              </div>
            </div>
          </div>

          {/* Revolution Info */}
          <div className="bg-blue-900/20 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-white mb-3">Revolution Effects</h3>
            <ul className="text-gray-300 text-sm space-y-2">
              <li>• Duration: 24 hours</li>
              <li>• Only citizens of this region can participate</li>
              <li>• If attackers win: Region becomes independent state</li>
              <li>• If defenders win: No changes to region ownership</li>
              <li>• New state will have immediate elections if created</li>
            </ul>
          </div>

          {/* Declaration Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* <div>
              <label htmlFor="declaration" className="block text-sm font-medium text-gray-300 mb-2">
                Revolution Declaration
              </label>
              <textarea
                id="declaration"
                value={declaration}
                onChange={(e) => setDeclaration(e.target.value)}
                placeholder="Declare your reasons for revolution..."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                rows={4}
                maxLength={500}
                required
              />
              <p className="text-xs text-gray-400 mt-1">
                {declaration.length}/500 characters
              </p>
            </div> */}

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || !canStartRevolution()}
                className={`flex-1 px-4 py-2 rounded-md font-medium transition-colors ${
                  canStartRevolution() && !loading
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                }`}
              >
                {loading ? 'Starting Revolution...' : 'Start Revolution'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RevolutionWarModal;
