import { api } from './api';
import { Factory, FactoryType, WorkSession } from '../../types/factory';

export interface CreateFactoryDto {
  name: string;
  type: FactoryType;
  regionId: string;
  wage: number;
  resourcePerWork: number;
  energyCost: number;
  maxWorkers: number;
}

export interface UpdateFactoryDto {
  name?: string;
  wage?: number;
  resourcePerWork?: number;
  energyCost?: number;
  maxWorkers?: number;
}

export interface WorkAtFactoryDto {
  factoryId: string;
}

export interface AutoWorkDto {
  enable: boolean;
}

export const factoryService = {
  // Get all factories
  getAllFactories: async (): Promise<Factory[]> => {
    const response = await api.get('/factories');
    return response.data;
  },

  // Get a single factory by ID
  getFactory: async (id: number): Promise<Factory> => {
    const response = await api.get(`/factories/${id}`);
    return response.data;
  },

  // Create a new factory
  createFactory: async (factoryData: Omit<Factory, 'id' | 'ownerId' | 'createdAt' | 'updatedAt'>): Promise<Factory> => {
    const response = await api.post('/factories', factoryData);
    return response.data;
  },

  // Update a factory
  updateFactory: async (id: number, factoryData: Partial<Factory>): Promise<Factory> => {
    const response = await api.patch(`/factories/${id}`, factoryData);
    return response.data;
  },

  // Work at a factory
  workAtFactory: async (id: number, energySpent: number): Promise<WorkSession> => {
    const response = await api.post(`/factories/${id}/work`, { energySpent });
    return response.data;
  },

  // Get work history
  getWorkHistory: async (): Promise<WorkSession[]> => {
    const response = await api.get('/factories/work-history');
    return response.data;
  },

  // Get factories by region
  getFactoriesByRegion: async (regionId: string): Promise<Factory[]> => {
    const response = await api.get(`/factories?regionId=${regionId}`);
    return response.data;
  },

  // Enable or disable auto work mode for a factory
  setAutoWorkMode: async (id: number, autoWorkData: AutoWorkDto): Promise<any> => {
    const response = await api.post(`/factories/${id}/auto-work`, autoWorkData);
    return response.data;
  }
};
