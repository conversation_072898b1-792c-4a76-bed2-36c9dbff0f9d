import { ReactNode } from 'react';

// Base interface for items that can be displayed in the modal
export interface SearchableItem {
  id: string | number;
  name?: string;
  username?: string;
  title?: string;
  description?: string;
  location?: string;
}

// Region-specific interface
export interface Region extends SearchableItem {
  name: string;
  population?: number;
  status?: string;
  location?: string;
  state?: {
    id: string | number;
    name: string;
  };
}

// Player-specific interface
export interface Player extends SearchableItem {
  username: string;
  level?: number;
  strength?: number;
  intelligence?: number;
  endurance?: number;
  isActive?: boolean;
}

// Props interface for SearchableModal component
export interface SearchableModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  icon?: React.ComponentType<{ className?: string }>;
  data: SearchableItem[];
  loading?: boolean;
  onItemClick?: (item: SearchableItem) => void;
  renderItem?: (item: SearchableItem) => ReactNode;
  searchPlaceholder?: string;
  searchFilter?: (items: SearchableItem[], searchTerm: string) => SearchableItem[];
}

// Custom search filter function type
export type SearchFilterFunction = (items: SearchableItem[], searchTerm: string) => SearchableItem[];
