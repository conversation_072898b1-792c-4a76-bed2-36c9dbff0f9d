import { create } from "zustand";
import { stateElectionService } from "../services/api/stateElection.service";
import { showErrorToast } from "../utils/showErrorToast";
import { normalizeElectionData } from "../utils/electionUtils";
import { ELECTION_CONFIG } from "../config/election.config";

const useElectionStore = create((set, get) => ({
  // State
  activeElection: null,
  electionHistory: [],
  loading: false,
  error: null,
  lastUpdated: null,

  // Pagination for history
  historyPage: ELECTION_CONFIG.PAGINATION.DEFAULT_PAGE,
  historyLimit: ELECTION_CONFIG.PAGINATION.DEFAULT_LIMIT,
  historyTotal: 0,
  historyTotalPages: 0,

  // Actions
  fetchActiveElection: async (stateId, force = false) => {
    set({ loading: true, error: null });

    try {
      const election = await stateElectionService.getActiveElection(stateId);
      const normalizedElection = normalizeElectionData(election);
      set({
        activeElection: normalizedElection,
        loading: false,
        lastUpdated: Date.now()
      });
      return normalizedElection;
    } catch (error) {
      console.error('Failed to fetch active election:', error);
      set({
        error: 'Failed to fetch active election',
        loading: false
      });
      showErrorToast('Failed to load election data');
      return null;
    }
  },

  fetchElectionHistory: async (stateId, page = 1, limit = 10) => {
    set({ loading: true, error: null });

    try {
      const history = await stateElectionService.getElectionHistory(stateId, page, limit);

      // Handle backend returning array directly vs wrapped object
      const electionsArray = Array.isArray(history) ? history : (history.elections || []);
      const normalizedElections = electionsArray.map(normalizeElectionData);

      // Calculate pagination info if not provided by backend
      const totalElections = Array.isArray(history) ? history.length : (history.total || normalizedElections.length);
      const totalPages = Math.ceil(totalElections / limit);

      set({
        electionHistory: normalizedElections,
        historyPage: Array.isArray(history) ? page : (history.page || page),
        historyLimit: Array.isArray(history) ? limit : (history.limit || limit),
        historyTotal: totalElections,
        historyTotalPages: totalPages,
        loading: false
      });
      return {
        elections: normalizedElections,
        page: page,
        limit: limit,
        total: totalElections,
        totalPages: totalPages
      };
    } catch (error) {
      console.error('Failed to fetch election history:', error);
      set({
        error: 'Failed to fetch election history',
        loading: false
      });
      showErrorToast('Failed to load election history');
      return null;
    }
  },

  submitVote: async (electionId, candidateId) => {
    set({ loading: true, error: null });

    try {
      const updatedElection = await stateElectionService.submitVote(electionId, { candidateId });
      const normalizedElection = normalizeElectionData(updatedElection);
      set({
        activeElection: normalizedElection,
        loading: false,
        lastUpdated: Date.now()
      });
      return normalizedElection;
    } catch (error) {
      console.error('Failed to submit vote:', error);
      set({
        error: 'Failed to submit vote',
        loading: false
      });
      throw error; // Re-throw to handle in component
    }
  },

  refreshActiveElection: async (stateId) => {
    return get().fetchActiveElection(stateId, true);
  },

  clearElectionData: () => {
    set({
      activeElection: null,
      electionHistory: [],
      error: null,
      lastUpdated: null,
      historyPage: ELECTION_CONFIG.PAGINATION.DEFAULT_PAGE,
      historyTotal: 0,
      historyTotalPages: 0
    });
  },

  updateElectionInRealTime: (updatedElection) => {
    set({
      activeElection: updatedElection,
      lastUpdated: Date.now()
    });
  }
}));

export default useElectionStore;
