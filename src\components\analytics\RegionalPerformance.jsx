import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';

const RegionalPerformance = ({ regionId }) => {
  const [performance, setPerformance] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPerformance = async () => {
      if (!regionId) return;
      
      try {
        setLoading(true);
        const data = await warService.getRegionalPerformance(regionId);
        setPerformance(data);
      } catch (error) {
        console.error('Failed to fetch regional performance:', error);
        toast.error('Failed to load regional performance data');
      } finally {
        setLoading(false);
      }
    };

    fetchPerformance();
  }, [regionId]);

  if (!regionId) {
    return null;
  }

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6 animate-pulse">
        <div className="h-6 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="grid grid-cols-2 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-700 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!performance) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <p className="text-gray-400">No performance data available for this region</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">Regional Performance: {performance.regionName}</h2>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Total Wars</h3>
            <span className="text-neonBlue text-2xl">⚔️</span>
          </div>
          <p className="text-2xl font-bold text-white">{performance.totalWars}</p>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Wars Won</h3>
            <span className="text-green-500 text-2xl">🏆</span>
          </div>
          <p className="text-2xl font-bold text-white">{performance.warsWon}</p>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Wars Lost</h3>
            <span className="text-red-500 text-2xl">❌</span>
          </div>
          <p className="text-2xl font-bold text-white">{performance.warsLost}</p>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Win Rate</h3>
            <span className="text-yellow-500 text-2xl">📊</span>
          </div>
          <p className="text-2xl font-bold text-white">{(performance.winRate).toFixed(0)}%</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Damage Statistics</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Damage Dealt:</span>
              <span className="text-white">{performance.totalDamageDealt.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Damage Received:</span>
              <span className="text-white">{performance.totalDamageReceived.toLocaleString()}</span>
            </div>
            <div className="flex justify-between border-t border-gray-600 pt-2 mt-2">
              <span className="text-gray-400">Damage Ratio:</span>
              <span className={performance.damageRatio >= 1 ? 'text-green-400' : 'text-red-400'}>
                {performance.damageRatio.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Performance Metrics</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-400">Win Rate</span>
                <span className="text-sm text-white">{(performance.winRate).toFixed(0)}%</span>
              </div>
              <div className="w-full bg-gray-600 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full" 
                  style={{ width: `${performance.winRate}%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-400">Damage Ratio</span>
                <span className="text-sm text-white">{performance.damageRatio.toFixed(2)}</span>
              </div>
              <div className="w-full bg-gray-600 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${performance.damageRatio >= 1 ? 'bg-green-500' : 'bg-red-500'}`}
                  style={{ width: `${Math.min(performance.damageRatio, 2) * 50}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-6">
        <h3 className="text-lg font-medium text-white mb-3">Most Active Warriors</h3>
        <div className="bg-gray-700 rounded-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-600">
            <thead className="bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Player
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Damage
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-600">
              {performance.mostActiveWarriors.map((warrior) => (
                <tr key={warrior.userId}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link to={`/users/${warrior.userId}`} className="text-neonBlue hover:text-blue-400">
                      {warrior.username}
                    </Link>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-white">
                    {warrior.damage.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default RegionalPerformance;
