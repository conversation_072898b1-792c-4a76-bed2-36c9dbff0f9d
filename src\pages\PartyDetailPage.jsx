import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import Navbar from "../components/Navbar";
import { partyService } from "../services/api/party.service";
import { useAuthGuard } from "../hooks/useAuthGuard";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import {
  FaCrown,
  FaUsers,
  FaUserPlus,
  FaUserMinus,
  FaSpinner,
  FaCheck,
  FaTimes,
  FaExchangeAlt,
  FaEdit,
} from "react-icons/fa";
import useUserDataStore from "../store/useUserDataStore";

const PartyDetailPage = () => {
  useAuthGuard();
  const { id } = useParams();
  const navigate = useNavigate();
  const { userData: user, fetchUserData } = useUserDataStore();
  const [party, setParty] = useState(null);
  const [joinRequests, setJoinRequests] = useState([]);
  const [userJoinRequest, setUserJoinRequest] = useState(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showRequestsModal, setShowRequestsModal] = useState(false);
  const [showTransferLeadershipModal, setShowTransferLeadershipModal] =
    useState(false);
  const [selectedNewLeader, setSelectedNewLeader] = useState(null);

  const [isEditingName, setIsEditingName] = useState(false);
  const [newName, setNewName] = useState("");
  const [isUpdatingName, setIsUpdatingName] = useState(false);

  // Check if user is the party leader
  const isPartyLeader = party?.leader?.id === user?.id;

  // Check if user is a member of the party
  const isPartyMember = party?.members?.some(
    (member) => member.id === user?.id
  );

  useEffect(() => {
    const fetchPartyData = async () => {
      setLoading(true);
      try {
        const partyData = await partyService.getPartyById(id);
        setParty(partyData);

        // If user is the party leader, fetch join requests
        if (partyData.leader.id === user?.id) {
          const requests = await partyService.getPartyJoinRequests(id);
          setJoinRequests(requests);
        } else {
          // Check if the user has a pending join request for this party
          const userRequests = await partyService.getUserJoinRequests();
          const pendingRequest = userRequests.find(
            (req) => req.partyId === id && req.status === "pending"
          );
          setUserJoinRequest(pendingRequest || null);
        }

        setError(null);
      } catch (err) {
        console.error("Error fetching party data:", err);
        setError("Failed to load party details. Please try again later.");
        showErrorToast(err);
      } finally {
        setLoading(false);
      }
    };

    if (id && user) {
      fetchPartyData();
    }
  }, [id, user]);

  const handleJoinRequest = async () => {
    setActionLoading(true);
    try {
      const response = await partyService.createJoinRequest(id);
      setUserJoinRequest(response);
      showSuccessToast("Join request sent successfully!");
    } catch (err) {
      console.error("Error sending join request:", err);
      showErrorToast(err);
    } finally {
      setActionLoading(false);
    }
  };

  const handleCancelRequest = async () => {
    if (!userJoinRequest) return;

    setActionLoading(true);
    try {
      await partyService.cancelJoinRequest(userJoinRequest.id);
      setUserJoinRequest(null);
      showSuccessToast("Join request cancelled successfully!");
    } catch (err) {
      console.error("Error cancelling join request:", err);
      showErrorToast(err);
    } finally {
      setActionLoading(false);
    }
  };

  const handleLeaveParty = async () => {
    setActionLoading(true);
    if (
      !window.confirm(
        "Are you sure you want to disband the party? Transfer leadership to another member before leaving if you dont want to disband the party"
      )
    ) {
      return;
    }
    try {
      await partyService.leaveParty(id);
      showSuccessToast("You have left the party.");
      navigate("/profile");
    } catch (err) {
      console.error("Error leaving party:", err);
      showErrorToast(err);
    } finally {
      setActionLoading(false);
    }
  };

  const handleAcceptRequest = async (requestId) => {
    setActionLoading(true);
    try {
      const updatedParty = await partyService.acceptJoinRequest(requestId);
      setParty(updatedParty);

      // Remove the request from the list
      setJoinRequests((prevRequests) =>
        prevRequests.filter((req) => req.id !== requestId)
      );

      showSuccessToast("Join request accepted!");
    } catch (err) {
      console.error("Error accepting join request:", err);
      showErrorToast(err);
    } finally {
      setActionLoading(false);
    }
  };

  const handleRejectRequest = async (requestId) => {
    setActionLoading(true);
    try {
      await partyService.rejectJoinRequest(requestId);

      // Remove the request from the list
      setJoinRequests((prevRequests) =>
        prevRequests.filter((req) => req.id !== requestId)
      );

      showSuccessToast("Join request rejected.");
    } catch (err) {
      console.error("Error rejecting join request:", err);
      showErrorToast(err);
    } finally {
      setActionLoading(false);
    }
  };

  const handleTransferLeadership = async (newLeaderId) => {
    if (!isPartyLeader || !newLeaderId) return;

    setActionLoading(true);
    try {
      const updatedParty = await partyService.transferLeadership(
        id,
        newLeaderId
      );
      setParty(updatedParty);
      setShowTransferLeadershipModal(false);
      setSelectedNewLeader(null);
      showSuccessToast("Leadership transferred successfully!");
    } catch (err) {
      console.error("Error transferring leadership:", err);
      showErrorToast(err);
    } finally {
      setActionLoading(false);
    }
  };

  const handleKickMember = async (memberId) => {
    if (!isPartyLeader || !memberId) return;

    if (
      !window.confirm(
        "Are you sure you want to kick this member from the party?"
      )
    ) {
      return;
    }

    setActionLoading(true);
    try {
      const updatedParty = await partyService.kickMember(id, memberId);
      setParty(updatedParty);
      showSuccessToast("Member has been kicked from the party.");
    } catch (err) {
      console.error("Error kicking member:", err);
      showErrorToast(err);
    } finally {
      setActionLoading(false);
    }
  };

  // Username editing functions
  const handleEditName = () => {
    setNewName(party?.name || "");
    setIsEditingName(true);
  };

  const handleCancelEdit = () => {
    setIsEditingName(false);
    setNewName("");
  };

  const handleSaveName = async () => {
    if (!newName.trim()) {
      showErrorToast("Name cannot be empty");
      return;
    }

    if (newName === party?.name) {
      setIsEditingName(false);
      return;
    }

    setIsUpdatingName(true);
    try {
      await partyService.updateParty({
        name: newName.trim(),
        id: party.id,
      });
      showSuccessToast("Name updated successfully!");

      // Force refresh the user data to reflect the change
      await fetchUserData(true);

      setIsEditingName(false);
      setNewName("");
    } catch (error) {
      showErrorToast(error || "Failed to update Name");
    } finally {
      setIsUpdatingName(false);
    }
  };

  const handleNameKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSaveName();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <FaSpinner className="animate-spin text-4xl text-neonBlue" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !party) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold text-red-500 mb-4">Error</h2>
            <p className="text-white">{error || "Party not found"}</p>
            <Link
              to="/profile"
              className="mt-4 inline-block text-blue-500 hover:text-blue-400"
            >
              Return to Profile
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/profile" className="text-blue-600 hover:text-blue-800">
            ← Back to Profile
          </Link>
        </div>

        {/* Party Details Card */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div className="flex items-center mb-4 md:mb-0">
              <div>
                {isEditingName ? (
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={newName}
                      onChange={(e) => setNewName(e.target.value)}
                      onKeyDown={handleNameKeyPress}
                      className="w-30 font-bold bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 focus:border-neonBlue focus:outline-none"
                      placeholder="Enter Name"
                      maxLength={20}
                      disabled={isUpdatingName}
                    />
                    <button
                      onClick={handleSaveName}
                      disabled={isUpdatingName}
                      className="text-green-400 hover:text-green-300 p-2 disabled:opacity-50"
                      title="Save Name"
                    >
                      <FaCheck />
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      disabled={isUpdatingName}
                      className="text-red-400 hover:text-red-300 p-2 disabled:opacity-50"
                      title="Cancel edit"
                    >
                      <FaTimes />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <h1 className="text-3xl font-bold text-neonBlue">
                      {party?.name}
                    </h1>
                    {isPartyLeader && (
                      <button
                        onClick={handleEditName}
                        className="text-gray-400 hover:text-neonBlue p-1 ml-2"
                        title="Edit Name"
                      >
                        <FaEdit />
                      </button>
                    )}
                  </div>
                )}
                <div className="flex items-center text-gray-400">
                  <span className="mr-2">Region:</span>
                  <Link
                    to={`/regions/${party?.region?.id}`}
                    className="text-blue-500 hover:text-blue-400"
                  >
                    {party?.region?.name}
                  </Link>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="w-full md:w-auto">
              {isPartyMember ? (
                <button
                  onClick={handleLeaveParty}
                  disabled={actionLoading}
                  className={`flex items-center justify-center px-4 py-2 rounded-md ${"bg-red-600 hover:bg-red-700"} text-white w-full md:w-auto`}
                >
                  {actionLoading ? (
                    <FaSpinner className="animate-spin mr-2" />
                  ) : (
                    <FaUserMinus className="mr-2" />
                  )}
                  {"Leave Party"}
                </button>
              ) : userJoinRequest ? (
                <button
                  onClick={handleCancelRequest}
                  disabled={actionLoading}
                  className="flex items-center justify-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-md text-white w-full md:w-auto"
                >
                  {actionLoading ? (
                    <FaSpinner className="animate-spin mr-2" />
                  ) : (
                    <FaTimes className="mr-2" />
                  )}
                  Cancel Join Request
                </button>
              ) : (
                <button
                  onClick={handleJoinRequest}
                  disabled={actionLoading}
                  className="flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white w-full md:w-auto"
                >
                  {actionLoading ? (
                    <FaSpinner className="animate-spin mr-2" />
                  ) : (
                    <FaUserPlus className="mr-2" />
                  )}
                  Request to Join
                </button>
              )}
            </div>
          </div>

          {/* Party Description */}
          {party.description && (
            <div className="mb-6 p-4 bg-gray-700 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-2">
                Description
              </h3>
              <p className="text-gray-300">{party.description}</p>
            </div>
          )}

          {/* Party Leader */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <FaCrown className="text-yellow-400 mr-2" /> Party Leader
              </h3>

              {/* Transfer Leadership Button (Only for party leader) */}
              {isPartyLeader && party.members.length > 1 && (
                <button
                  onClick={() => setShowTransferLeadershipModal(true)}
                  className="text-sm bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded-md flex items-center"
                >
                  <FaExchangeAlt className="mr-1" />
                  Transfer Leadership
                </button>
              )}
            </div>
            <div className="bg-gray-700 rounded-lg p-4 flex items-center">
              <div className="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center text-xl text-neonBlue mr-3">
                {party?.leader?.username.charAt(0).toUpperCase()}
              </div>
              <div>
                <Link
                  to={`/users/${party?.leader?.id}`}
                  className="text-blue-500 hover:text-blue-400 font-medium"
                >
                  {party?.leader?.username}
                </Link>
              </div>
            </div>
          </div>

          {/* Party Members */}
          <div>
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <FaUsers className="text-neonBlue mr-2" /> Members (
                {party?.members?.length})
              </h3>

              {/* Join Requests Button (Only for party leader) */}
              {isPartyLeader && joinRequests.length > 0 && (
                <button
                  onClick={() => setShowRequestsModal(true)}
                  className="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md flex items-center"
                >
                  <FaUserPlus className="mr-1" />
                  Join Requests ({joinRequests.length})
                </button>
              )}
            </div>

            <div className="bg-gray-700 rounded-lg p-4">
              {party?.members?.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {party.members.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center justify-between bg-gray-800 rounded-lg p-3"
                    >
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center text-lg text-neonBlue mr-3">
                          {member.username.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <Link
                            to={`/users/${member.id}`}
                            className="text-blue-500 hover:text-blue-400"
                          >
                            {member.username}
                          </Link>
                          {member.id === party.leader.id && (
                            <span className="ml-2 text-yellow-400 text-xs">
                              (Leader)
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Kick button - only shown to leader and not for themselves */}
                      {isPartyLeader && member.id !== party.leader.id && (
                        <button
                          onClick={() => handleKickMember(member.id)}
                          disabled={actionLoading}
                          className="text-red-400 hover:text-red-300 p-1 rounded-full hover:bg-gray-700 transition-colors"
                          title="Kick member"
                        >
                          <FaUserMinus />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400">No members in this party yet.</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Join Requests Modal */}
      {showRequestsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-neonBlue">Join Requests</h2>
              <button
                onClick={() => setShowRequestsModal(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {joinRequests.map((request) => (
                <div key={request.id} className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center text-lg text-neonBlue mr-3">
                      {request.username.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <Link
                        to={`/users/${request.userId}`}
                        className="text-blue-500 hover:text-blue-400"
                      >
                        {request.username}
                      </Link>
                      <p className="text-xs text-gray-400">
                        Requested:{" "}
                        {new Date(request.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleAcceptRequest(request.id)}
                      disabled={actionLoading}
                      className="flex-1 flex items-center justify-center px-3 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white"
                    >
                      <FaCheck className="mr-1" /> Accept
                    </button>
                    <button
                      onClick={() => handleRejectRequest(request.id)}
                      disabled={actionLoading}
                      className="flex-1 flex items-center justify-center px-3 py-2 bg-red-600 hover:bg-red-700 rounded-md text-white"
                    >
                      <FaTimes className="mr-1" /> Reject
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Transfer Leadership Modal */}
      {showTransferLeadershipModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-yellow-400">
                Transfer Leadership
              </h2>
              <button
                onClick={() => {
                  setShowTransferLeadershipModal(false);
                  setSelectedNewLeader(null);
                }}
                className="text-gray-400 hover:text-white"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <p className="text-gray-300 mb-4">
              Select a member to transfer party leadership to. This action
              cannot be undone.
            </p>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {party.members
                .filter((member) => member.id !== party.leader.id) // Exclude current leader
                .map((member) => (
                  <div
                    key={member.id}
                    className={`bg-gray-700 rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedNewLeader === member.id
                        ? "border-2 border-yellow-400"
                        : "hover:bg-gray-600"
                    }`}
                    onClick={() => setSelectedNewLeader(member.id)}
                  >
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center text-lg text-neonBlue mr-3">
                        {member.username.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <Link
                          to={`/users/${member.id}`}
                          className="text-blue-500 hover:text-blue-400"
                          onClick={(e) => e.stopPropagation()} // Prevent selecting when clicking the link
                        >
                          {member.username}
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowTransferLeadershipModal(false);
                  setSelectedNewLeader(null);
                }}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-md text-white"
              >
                Cancel
              </button>
              <button
                onClick={() => handleTransferLeadership(selectedNewLeader)}
                disabled={actionLoading || !selectedNewLeader}
                className={`px-4 py-2 rounded-md text-white flex items-center ${
                  !selectedNewLeader
                    ? "bg-gray-500 cursor-not-allowed"
                    : "bg-yellow-600 hover:bg-yellow-700"
                }`}
              >
                {actionLoading ? (
                  <FaSpinner className="animate-spin mr-2" />
                ) : (
                  <FaExchangeAlt className="mr-2" />
                )}
                Transfer Leadership
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PartyDetailPage;
