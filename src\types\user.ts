import { Party } from './party';
import { Region } from './region';
import { Factory } from './factory';
import { WorkSession } from './factory';

export interface User {
  id: number;
  username: string;
  email: string;
  avatarUrl?: string;
  level: number;
  experience: number;
  energy: number;
  strength: number;
  endurance: number;
  intelligence: number;
  gold: number;
  money: number;
  trainingExpiresAt?: Date;
  aboutMe?: string;
  isPremium: boolean;
  premiumExpiresAt?: Date;
  isActive: boolean;
  leadingParty?: Party;
  memberOfParty?: Party;
  region: Region;
  ownedFactories?: Factory[];
  workSessions?: WorkSession[];
  createdAt: Date;
  updatedAt: Date;
} 