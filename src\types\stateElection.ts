import { User } from './user';
import { State } from './state';

export enum ElectionStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface Candidate {
  id: string;
  user: User;
  party?: {
    id: string;
    name: string;
  };
  voteCount: number;
  manifesto?: string;
  createdAt: Date;
}

export interface Vote {
  id: string;
  voter: User;
  candidate: Candidate;
  createdAt: Date;
}

export interface StateElection {
  id: string;
  state: State;
  status: ElectionStatus;
  startTime: Date;
  endTime: Date;
  duration: number; // in hours, typically 48 (2 days)
  candidates: Candidate[];
  votes: Vote[];
  totalVotes: number;
  voterTurnout: number;
  winner?: Candidate;
  isUserEligible: boolean;
  hasUserVoted: boolean;
  userVote?: Vote;
  createdAt: Date;
  updatedAt: Date;
}

export interface ElectionHistory {
  elections: StateElection[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreateVoteDto {
  candidateId: string;
}

export interface ElectionStatistics {
  totalElections: number;
  totalVotes: number;
  averageTurnout: number;
  mostActiveState: {
    state: State;
    electionCount: number;
  };
  recentWinners: {
    candidate: Candidate;
    election: StateElection;
  }[];
}

export interface CandidateRegistrationDto {
  manifesto?: string;
}

export interface ElectionTimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  totalSeconds: number;
  isExpired: boolean;
}
