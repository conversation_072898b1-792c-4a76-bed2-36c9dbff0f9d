// Array of distinct colors for states
const baseColors = [
  '#FF6B6B', // Red
  '#4ECDC4', // Turquoise
  '#45B7D1', // Light Blue
  '#96CEB4', // Mint
  '#FFEEAD', // Light Yellow
  '#D4A5A5', // <PERSON>
  '#9B59B6', // <PERSON>
  '#3498DB', // Blue
  '#E67E22', // Orange
  '#2ECC71', // Green
  '#F1C40F', // Yellow
  '#E74C3C', // Dark Red
  '#1ABC9C', // Turquoise
  '#9B59B6', // Amethyst
  '#34495E', // Navy Blue
];

export const generateStateColor = (index) => {
  // If we run out of base colors, generate a random one
  if (index >= baseColors.length) {
    return `#${Math.floor(Math.random()*16777215).toString(16)}`;
  }
  return baseColors[index];
};