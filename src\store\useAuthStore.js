import { create } from "zustand";

const useAuthStore = create((set) => ({
    user: JSON.parse(localStorage.getItem("user")) || null,
  access_token: localStorage.getItem("access_token") || null,

  login: (user, access_token) => {
    localStorage.setItem("access_token", access_token);
    localStorage.setItem("user", JSON.stringify(user));
    set({ user: user, access_token });
  },

  logout: () => {
    // Clean up chat connections before logout
    try {
      // Dynamically import to avoid circular dependency
      import('./useChatStore.js').then(({ default: useChatStore }) => {
        const { reset } = useChatStore.getState();
        reset();
        console.log('AuthStore: Chat connections cleaned up on logout');
      }).catch(error => {
        console.warn('AuthStore: Failed to cleanup chat connections:', error);
      });
    } catch (error) {
      console.warn('AuthStore: Error during chat cleanup:', error);
    }

    localStorage.removeItem("access_token");
    localStorage.removeItem("user");
    set({ user: null, access_token: null });
  },
}));

export default useAuthStore;
