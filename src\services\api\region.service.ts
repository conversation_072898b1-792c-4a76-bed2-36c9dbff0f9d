import { api } from './api';
import { Region } from '../../types/region';

export interface RegionSearchResponse {
  regions: Region[];
  total: number;
}

export const regionService = {
  // Get all regions
  getAllRegions: async (): Promise<Region[]> => {
    const response = await api.get('/regions');
    return response.data;
  },

  // Get a single region by ID
  getRegion: async (id: string): Promise<Region> => {
    const response = await api.get(`/regions/${id}`);
    return response.data;
  },

  // Search regions by name
  searchRegionsByName: async (name: string): Promise<RegionSearchResponse> => {
    const response = await api.get(`/regions/search`, {
      params: { name }
    });
    return response.data;
  }
};

export default regionService;
