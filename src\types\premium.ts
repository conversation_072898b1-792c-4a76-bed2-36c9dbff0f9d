export enum PremiumPlan {
  MONTHLY = 'monthly',
  SEMIANNUAL = 'semiannual',
  YEARLY = 'yearly',
}

export enum GoldPackageType {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  EXTRA_LARGE = 'extra_large',
  CUSTOM = 'custom',
}

export interface SubscriptionPlan {
  id: PremiumPlan | string;
  name: string;
  price: string;
  period: string;
  features: string[];
  popular: boolean;
  savings: string | null;
}

export interface GoldPackage {
  id: GoldPackageType | string;
  name: string;
  amount: number;
  price: string;
  bonus?: number;
  popular: boolean;
}
