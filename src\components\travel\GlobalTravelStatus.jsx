import React, { useState, useEffect } from "react";
import { travelService } from "../../services/api/travel.service";
import { Link } from "react-router-dom";
import { FaMapMarkedAlt, FaHourglass } from "react-icons/fa";
import useTravelCountdown from "../../hooks/useTravelCountdown";

const GlobalTravelStatus = () => {
  const [currentTravel, setCurrentTravel] = useState(null);
  const [loading, setLoading] = useState(true);

  // Use our custom hook for countdown
  const {
    timeRemaining,
    formattedTime,
    isCompleted
  } = useTravelCountdown(currentTravel?.endTime);

  const fetchCurrentTravel = async () => {
    try {
      setLoading(true);
      const travel = await travelService.getCurrentTravel();
      setCurrentTravel(travel);
    } catch (err) {
      console.error("Error fetching travel status:", err);
    } finally {
      setLoading(false);
    }
  };

  // Check if travel is completed
  useEffect(() => {
    if (isCompleted && currentTravel?.status === 'in_progress') {
      // Only fetch once when travel completes
      fetchCurrentTravel();
    }
  }, [isCompleted, currentTravel?.status]);

  useEffect(() => {
    // Initial fetch when component mounts
    fetchCurrentTravel();

    // We don't need to poll the server frequently since we have the countdown timer
    // The timer is calculated client-side based on the endTime
    // Only fetch again when the travel should be complete
    let timeoutId;

    if (currentTravel?.status === 'in_progress' && currentTravel?.endTime) {
      const endTime = new Date(currentTravel.endTime).getTime();
      const now = new Date().getTime();
      const timeUntilCompletion = Math.max(0, endTime - now);

      // Set a single timeout to check status when travel should complete
      timeoutId = setTimeout(() => {
        fetchCurrentTravel();
      }, timeUntilCompletion + 1000);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [currentTravel?.status, currentTravel?.endTime]);

  // Don't render anything if:
  // - We're still loading
  // - There's no current travel
  // - The travel status is not 'in_progress'
  // - We don't have time remaining data
  if (loading || !currentTravel || currentTravel.status !== 'in_progress' || !timeRemaining) {
    return null;
  }

  return (
    <Link
      to={`/regions/${currentTravel.destinationRegion.id}`}
      className="flex items-center bg-blue-900/30 px-3 py-1 rounded-md hover:bg-blue-800/40 transition-colors"
    >
      <FaMapMarkedAlt className="text-blue-400 mr-2" />
      <div className="flex flex-col">
        <span className="text-xs text-gray-300">Traveling to {currentTravel.destinationRegion?.name || "destination"}</span>
        {timeRemaining && (
          <span className={`text-xs flex items-center ${
            timeRemaining.hours === 0 && timeRemaining.minutes === 0
              ? 'text-red-400 font-bold'
              : 'text-yellow-300'
          }`}>
            <FaHourglass className="mr-1" size={10} />
            {timeRemaining.hours > 0 && `${timeRemaining.hours}h `}
            {(timeRemaining.hours > 0 || timeRemaining.minutes > 0) && `${timeRemaining.minutes}m `}
            {timeRemaining.seconds}s
          </span>
        )}
      </div>
    </Link>
  );
};

export default GlobalTravelStatus;
