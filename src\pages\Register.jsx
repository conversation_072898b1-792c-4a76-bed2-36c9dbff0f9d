import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import api from "../services/api/api";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import { FaEye, FaEyeSlash } from "react-icons/fa";


export default function Register() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [username, setUsername] = useState("");
  const [loading, setLoading] = useState(false);
  const [regionId, setRegionId] = useState("");
  const [regions, setRegions] = useState([]);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();

  const handleRegister = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { status } = await api.post("/auth/register", {
        username,
        email,
        password,
        regionId,
      });

      if (status === 201) {
        showSuccessToast("Account created! Please check your email to verify your account");
        navigate("/login");
      }
    } catch (error) {
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchRegions = async () => {
      try {
        const { data } = await api.get("/regions");
        setRegions(data);
      } catch (error) {
        console.error("Failed to load regions");
      }
    };

    fetchRegions();
  }, []);

  return (
    <div className="h-screen flex items-center justify-center">
      <form
        className="bg-darkCard p-6 rounded-lg shadow-lg"
        onSubmit={handleRegister}
      >
        <h2 className="text-2xl text-neonBlue font-bold mb-4">Register</h2>

        <input
          type="text"
          className="w-full p-2 mb-3 rounded bg-gray-800 text-white"
          placeholder="Username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
        />

        <input
          type="email"
          className="w-full p-2 mb-3 rounded bg-gray-800 text-white"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />

<div className="relative mb-3">
  <input
    type={showPassword ? "text" : "password"}
    className="w-full p-2 pr-10 rounded bg-gray-800 text-white"
    placeholder="Password"
    value={password}
    onChange={(e) => setPassword(e.target.value)}
    required
  />
  <span
    className="absolute right-3 top-3 cursor-pointer text-gray-400"
    onClick={() => setShowPassword((prev) => !prev)}
  >
    {showPassword ? <FaEyeSlash /> : <FaEye />}
  </span>
</div>


        <select
          className="w-full p-2 mb-3 rounded bg-gray-800 text-white"
          value={regionId}
          onChange={(e) => setRegionId(e.target.value)}
          required
        >
          <option value="">Select your region</option>
          {regions.map((region) => (
            <option key={region.id} value={region.id}>
              {region.name}
            </option>
          ))}
        </select>

        <button
          type="submit"
          className="w-full bg-neonGreen p-2 rounded"
          disabled={loading}
        >
          {loading ? "Creating account..." : "Register"}
        </button>
      </form>
    </div>
  );
}
