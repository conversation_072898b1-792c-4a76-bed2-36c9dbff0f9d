import React, { useState, useRef, useEffect } from 'react';
import { Send, Smile } from 'lucide-react';
import { CHAT_CONFIG, validateMessage } from '../../config/chat.config.js';

/**
 * Message input component
 * Handles message composition with typing indicators and validation
 */
const MessageInput = ({
  onSendMessage,
  onTypingStart,
  onTypingStop,
  disabled = false,
  placeholder = "Type a message..."
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState('');
  const textareaRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Cleanup typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setMessage(value);
    setError('');

    // Validate message length
    if (value.length > CHAT_CONFIG.MESSAGES.MAX_LENGTH) {
      setError(CHAT_CONFIG.ERRORS.MESSAGE_TOO_LONG);
      return;
    }

    // Handle typing indicators
    if (value.trim() && !isTyping) {
      setIsTyping(true);
      onTypingStart?.();
    }

    // Debounce typing stop
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        onTypingStop?.();
      }
    }, CHAT_CONFIG.MESSAGES.TYPING_DEBOUNCE);
  };

  const handleKeyDown = (e) => {
    // Send message on Enter (but not Shift+Enter)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleSendMessage = () => {
    const trimmedMessage = message.trim();

    if (!trimmedMessage) {
      setError(CHAT_CONFIG.ERRORS.MESSAGE_EMPTY);
      return;
    }

    // Validate message
    const validation = validateMessage(trimmedMessage);
    if (!validation.valid) {
      setError(validation.error);
      return;
    }

    // Clear typing state
    if (isTyping) {
      setIsTyping(false);
      onTypingStop?.();
    }

    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Send message
    onSendMessage(trimmedMessage);

    // Clear input
    setMessage('');
    setError('');

    // Focus back to input
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  const handleBlur = () => {
    // Stop typing indicator when input loses focus
    if (isTyping) {
      setIsTyping(false);
      onTypingStop?.();
    }
  };

  const characterCount = message.length;
  const isNearLimit = characterCount > CHAT_CONFIG.MESSAGES.MAX_LENGTH * 0.8;
  const isOverLimit = characterCount > CHAT_CONFIG.MESSAGES.MAX_LENGTH;
  const canSend = message.trim() && !isOverLimit && !disabled;

  return (
    <div className="p-4 md:p-4 p-3 bg-gray-800">
      {/* Error Message */}
      {error && (
        <div className="mb-2 text-red-400 text-sm">
          {error}
        </div>
      )}

      {/* Input Container */}
      <div className="flex items-end gap-3 md:gap-3 gap-2">
        {/* Message Input */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className={`
              w-full bg-gray-700 border rounded-lg text-white placeholder-gray-400
              resize-none overflow-hidden transition-all duration-200 text-base
              focus:outline-none focus:ring-2 focus:ring-neonBlue focus:border-transparent
              px-4 py-3 pr-12 md:px-4 md:py-3 md:pr-12
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              ${error ? 'border-red-500 focus:ring-red-500' : 'border-gray-600'}
              ${isOverLimit ? 'border-red-500' : ''}
            `}
            style={{
              minHeight: '48px',
              maxHeight: '120px'
            }}
          />

          {/* Character Count */}
          {(isNearLimit || isOverLimit) && (
            <div className={`absolute bottom-1 right-12 text-xs ${
              isOverLimit ? 'text-red-400' : 'text-yellow-400'
            }`}>
              {characterCount}/{CHAT_CONFIG.MESSAGES.MAX_LENGTH}
            </div>
          )}

          {/* Emoji Button (placeholder for future feature) */}
          {/* <button
            type="button"
            className="absolute bottom-3 right-3 p-1 text-gray-400 hover:text-white transition-colors disabled:opacity-50"
            disabled={disabled}
            title="Emojis (coming soon)"
          >
            <Smile className="w-5 h-5" />
          </button> */}
        </div>

        {/* Send Button */}
        <button
          onClick={handleSendMessage}
          disabled={!canSend}
          className={`
            rounded-lg transition-all duration-200 flex items-center justify-center touch-manipulation
            p-3 md:p-3 min-w-[48px] min-h-[48px]
            ${canSend
              ? 'bg-neonBlue hover:bg-blue-600 active:bg-blue-700 text-white shadow-lg hover:shadow-xl'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }
          `}
          title={
            disabled ? 'Connecting...' :
            !message.trim() ? 'Type a message to send' :
            isOverLimit ? 'Message too long' :
            'Send message (Enter)'
          }
        >
          <Send className="w-5 h-5" />
        </button>
      </div>

      {/* Input Help Text */}
      <div className="mt-2 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-4 text-xs text-gray-400">
        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4">
          <span className="hidden sm:inline">Press Enter to send, Shift+Enter for new line</span>
          <span className="sm:hidden">Tap send button or press Enter</span>
          {disabled && (
            <span className="text-yellow-400">Connecting to chat server...</span>
          )}
        </div>

        {!isNearLimit && !error && (
          <span className="text-right sm:text-left">
            {characterCount > 0 && `${characterCount} characters`}
          </span>
        )}
      </div>

      {/* Typing Indicator Status */}
      {isTyping && (
        <div className="mt-1 text-xs text-gray-500">
          Typing...
        </div>
      )}
    </div>
  );
};

export default MessageInput;
