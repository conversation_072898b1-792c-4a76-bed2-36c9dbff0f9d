import { api } from './api';
import {
  StateElection,
  ElectionHistory,
  CreateVoteDto,
  ElectionStatistics,
  CandidateRegistrationDto
} from '../../types/stateElection';
import { ELECTION_CONFIG } from '../../config/election.config';

export const stateElectionService = {
  // Get active election for a specific state
  getActiveElection: async (stateId: string): Promise<StateElection | null> => {
    try {
      const response = await api.get(`/state-elections/state/${stateId}/active`);
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // No active election
      }
      throw error;
    }
  },

  // Get election history for a state with pagination
  getElectionHistory: async (
    stateId: string,
    page: number = ELECTION_CONFIG.PAGINATION.DEFAULT_PAGE,
    limit: number = ELECTION_CONFIG.PAGINATION.DEFAULT_LIMIT
  ): Promise<ElectionHistory> => {
    const response = await api.get(`/state-elections/state/${stateId}/history`, {
      params: { page, limit }
    });
    return response.data;
  },

  // Submit a vote for an election
  submitVote: async (electionId: string, voteData: CreateVoteDto): Promise<StateElection> => {
    const response = await api.post(`/state-elections/${electionId}/vote`, voteData);
    return response.data;
  },

  // Get a specific election by ID
  getElection: async (electionId: string): Promise<StateElection> => {
    const response = await api.get(`/state-elections/${electionId}`);
    return response.data;
  },

  // Get all elections (for admin/analytics)
  getAllElections: async (
    page: number = ELECTION_CONFIG.PAGINATION.DEFAULT_PAGE,
    limit: number = ELECTION_CONFIG.PAGINATION.DEFAULT_LIMIT,
    status?: string
  ): Promise<ElectionHistory> => {
    const response = await api.get('/state-elections', {
      params: { page, limit, status }
    });
    return response.data;
  },

  // Get election statistics
  getElectionStatistics: async (): Promise<ElectionStatistics> => {
    const response = await api.get('/state-elections/statistics');
    return response.data;
  },

  // Register as a candidate (if elections support candidate registration)
  registerAsCandidate: async (
    electionId: string,
    candidateData: CandidateRegistrationDto
  ): Promise<StateElection> => {
    const response = await api.post(`/state-elections/${electionId}/candidates`, candidateData);
    return response.data;
  },

  // Get user's voting history
  getUserVotingHistory: async (
    page: number = ELECTION_CONFIG.PAGINATION.DEFAULT_PAGE,
    limit: number = ELECTION_CONFIG.PAGINATION.DEFAULT_LIMIT
  ): Promise<ElectionHistory> => {
    const response = await api.get('/state-elections/user/history', {
      params: { page, limit }
    });
    return response.data;
  },

  // Get elections where user is a candidate
  getUserCandidacies: async (
    page: number = ELECTION_CONFIG.PAGINATION.DEFAULT_PAGE,
    limit: number = ELECTION_CONFIG.PAGINATION.DEFAULT_LIMIT
  ): Promise<ElectionHistory> => {
    const response = await api.get('/state-elections/user/candidacies', {
      params: { page, limit }
    });
    return response.data;
  }
};

export default stateElectionService;
