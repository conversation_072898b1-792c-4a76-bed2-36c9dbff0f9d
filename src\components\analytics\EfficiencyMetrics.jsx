import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';

const EfficiencyMetrics = ({ limit = 10 }) => {
  const [metrics, setMetrics] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        setLoading(true);
        const data = await warService.getEfficiencyMetrics(limit);
        setMetrics(data);
      } catch (error) {
        console.error('Failed to fetch efficiency metrics:', error);
        toast.error('Failed to load efficiency metrics');
      } finally {
        setLoading(false);
      }
    };

    fetchMetrics();
  }, [limit]);

  const getEfficiencyColor = (efficiency) => {
    if (efficiency >= 2.0) return 'text-green-400';
    if (efficiency >= 1.5) return 'text-lime-400';
    if (efficiency >= 1.0) return 'text-yellow-400';
    if (efficiency >= 0.5) return 'text-orange-400';
    return 'text-red-400';
  };

  const getWinRateColor = (winRate) => {
    if (winRate >= 0.8) return 'text-green-400';
    if (winRate >= 0.6) return 'text-lime-400';
    if (winRate >= 0.4) return 'text-yellow-400';
    if (winRate >= 0.2) return 'text-orange-400';
    return 'text-red-400';
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6 animate-pulse">
        <div className="h-6 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="h-64 bg-gray-700 rounded"></div>
      </div>
    );
  }

  if (metrics.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">Efficiency Metrics</h2>
        <p className="text-gray-400">No efficiency data available</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">Efficiency Metrics</h2>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-600">
          <thead className="bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Rank
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Player
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Efficiency
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Total Damage
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Energy Spent
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Wars
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Win Rate
              </th>
            </tr>
          </thead>
          <tbody className="bg-gray-800 divide-y divide-gray-700">
            {metrics.map((player, index) => (
              <tr key={player.userId} className={index % 2 === 0 ? 'bg-gray-750' : 'bg-gray-800'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                  {index + 1}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <Link to={`/users/${player.userId}`} className="text-neonBlue hover:text-blue-400">
                    {player.username}
                  </Link>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                  <span className={getEfficiencyColor(player.efficiency)}>
                    {player.efficiency.toFixed(2)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {player.totalDamage.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {player.totalEnergySpent.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white text-right">
                  {player.warCount}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                  <span className={getWinRateColor(player.winRate)}>
                    {(player.winRate * 100).toFixed(0)}%
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {metrics.length >= limit && (
        <div className="mt-6 text-center">
          <button 
            onClick={() => warService.getEfficiencyMetrics(limit + 10).then(setMetrics)}
            className="inline-block px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Load More
          </button>
        </div>
      )}
      
      <div className="mt-6 bg-gray-700 p-4 rounded-md">
        <h3 className="text-lg font-medium text-white mb-2">About Efficiency</h3>
        <p className="text-gray-300 text-sm">
          Efficiency is calculated as the ratio of damage dealt to energy spent. A higher efficiency means a player is getting more damage output per unit of energy invested in wars.
        </p>
      </div>
    </div>
  );
};

export default EfficiencyMetrics;
