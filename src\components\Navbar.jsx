import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { MessageCircle } from 'lucide-react';
import api from '../services/api/api';
import { useUserResources } from '../hooks/useUserResources';
import useUserDataStore from '../store/useUserDataStore';
import useAuthStore from '../store/useAuthStore';
import useResourcesStore from '../store/useResourcesStore';
import useChatStore from '../store/useChatStore';
import ChatInterface from './chat/ChatInterface.jsx';


export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const navigate = useNavigate();
  const { money, gold, loading, refetch } = useUserResources();
  const { userData, fetchUserData, clearUserData } = useUserDataStore();
  const { logout } = useAuthStore();
  const totalUnreadCount = useChatStore(state => state.totalUnreadCount);

  // Fetch user data when component mounts
  useEffect(() => {
    // Fetch user data once when the component mounts
    fetchUserData(false);
    refetch();

    // No interval for automatic refreshing
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleLogout = async () => {
    try {
      // Call the API to logout on the server
      await api.post('/auth/logout');
      clearUserData();
      logout();

      // Navigate to login page
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <nav className="bg-gray-800 border-b border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo and Brand */}
          <div className="flex items-center flex-shrink-0">
            <Link to="/home" className="flex items-center">
              <img src="/wn-logo.png" alt="Warfront Nations Logo" className="h-18 py-1 mr-4" style={{ width: 'auto' }} />
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <Link
                to="/home"
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                Home
              </Link>
              <Link
                to="/map"
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                Map
              </Link>
              <Link
                to="/jobs"
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                Jobs
              </Link>
              <Link
                to="/states"
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                States
              </Link>
              <Link
                to="/wars"
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                Wars
              </Link>
              <Link
                to="/travel/permissions"
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                Travel
              </Link>
              <Link
                to="/elections"
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                Elections
              </Link>
              {/* <Link
                to="/wars/analytics"
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                Analytics
              </Link> */}
              <Link
                to="/profile"
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                Profile
              </Link>
              <Link
                to="/shop"
                className="text-yellow-400 hover:text-yellow-300 px-3 py-2 rounded-md text-sm font-medium flex items-center"
              >
                <span className="mr-1">🪙</span> Shop
              </Link>

              {/* Chat Button */}
              <button
                onClick={() => setIsChatOpen(true)}
                className="relative text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors"
                title="Chat"
              >
                <MessageCircle className="w-5 h-5" />
                {totalUnreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                    {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Resources Display */}
          <div className="hidden md:flex items-center space-x-4 mr-4">
            <div className="flex items-center space-x-2">
              <span className="text-yellow-400">💰</span>
              <span className="text-gray-300">{loading ? "..." : userData?.money?.toLocaleString()}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-yellow-400">🪙</span>
              <span className="text-gray-300">{loading ? "..." : userData?.gold?.toLocaleString()}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-blue-400">⚡</span>
              <div className="flex flex-col">
                <div className="flex items-center">
                  <span className="text-gray-300 text-xs mr-1">Energy:</span>
                  <span className="text-gray-300 text-sm">{loading ? "..." : userData?.energy || 0}</span>
                </div>
                <div className="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-blue-500 transition-all duration-300"
                    style={{ width: `${userData?.energy || 0}%` }}
                  ></div>
                </div>
              </div>
            </div>


          </div>

          {/* User Menu */}
          <div className="hidden md:block">
            <div className="ml-4 flex items-center md:ml-15">
              <button
                onClick={handleLogout}
                className="text-gray-300 hover:text-neonBlue px-3 py-2 rounded-md text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-neonBlue hover:bg-gray-700 focus:outline-none"
            >
              <span className="sr-only">Open main menu</span>
              {!isMenuOpen ? (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {/* Logo for Mobile */}
            {/* <div className="flex justify-center py-2 border-b border-gray-700 mb-2">
              <Link to="/home" className="flex items-center">
                <img src="/wn-logo.png" alt="Warfront Nations Logo" className="h-14 py-2" style={{ width: 'auto' }} />
              </Link>
            </div> */}
            {/* Resources Display for Mobile */}
            <div className="flex justify-around py-2 border-b border-gray-700">
              <div className="flex items-center space-x-2">
                <span className="text-yellow-400">💰</span>
                <span className="text-gray-300">{loading ? "..." : userData?.money?.toLocaleString()}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-yellow-400">🪙</span>
                <span className="text-gray-300">{loading ? "..." : userData?.gold?.toLocaleString()}</span>
              </div>
            </div>
            {/* Energy Display for Mobile */}
            <div className="flex justify-center py-2 border-b border-gray-700">
              <div className="flex items-center space-x-2">
                <span className="text-blue-400">⚡</span>
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <span className="text-gray-300 text-xs mr-1">Energy:</span>
                    <span className="text-gray-300">{loading ? "..." : userData?.energy || 0}</span>
                  </div>
                  <div className="w-24 h-2 bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-500 transition-all duration-300"
                      style={{ width: `${userData?.energy || 0}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>


            <Link
              to="/home"
              className="text-gray-300 hover:text-neonBlue block px-3 py-2 rounded-md text-base font-medium"
            >
              Home
            </Link>
            <Link
              to="/map"
              className="text-gray-300 hover:text-neonBlue block px-3 py-2 rounded-md text-base font-medium"
            >
              Map
            </Link>
            <Link
              to="/jobs"
              className="text-gray-300 hover:text-neonBlue block px-3 py-2 rounded-md text-base font-medium"
            >
              Jobs
            </Link>
            <Link
              to="/states"
              className="text-gray-300 hover:text-neonBlue block px-3 py-2 rounded-md text-base font-medium"
            >
              States
            </Link>
            <Link
              to="/wars"
              className="text-gray-300 hover:text-neonBlue block px-3 py-2 rounded-md text-base font-medium"
            >
              Wars
            </Link>
              <Link
                to="/travel/permissions"
                className="text-gray-300 hover:text-neonBlue block px-3 py-2 rounded-md text-base font-medium"
              >
                Travel
              </Link>
            <Link
              to="/elections"
              className="text-gray-300 hover:text-neonBlue block px-3 py-2 rounded-md text-base font-medium"
            >
              Elections
            </Link>
            <Link
              to="/profile"
              className="text-gray-300 hover:text-neonBlue block px-3 py-2 rounded-md text-base font-medium"
            >
              Profile
            </Link>
            <Link
              to="/shop"
              className="text-yellow-400 hover:text-yellow-300 block px-3 py-2 rounded-md text-base font-medium flex items-center"
            >
              <span className="mr-1">🪙</span> Shop
            </Link>

            {/* Chat Button - Mobile */}
            <button
              onClick={() => setIsChatOpen(true)}
              className="relative text-gray-300 hover:text-neonBlue block w-full text-left px-3 py-2 rounded-md text-base font-medium flex items-center"
            >
              <MessageCircle className="w-5 h-5 mr-2" />
              Chat
              {totalUnreadCount > 0 && (
                <span className="ml-auto bg-red-500 text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                  {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
                </span>
              )}
            </button>

            <button
              onClick={handleLogout}
              className="text-gray-300 hover:text-neonBlue block w-full text-left px-3 py-2 rounded-md text-base font-medium"
            >
              Logout
            </button>
          </div>
        </div>
      )}

      {/* Chat Interface Modal */}
      <ChatInterface
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
      />
    </nav>
  );
}