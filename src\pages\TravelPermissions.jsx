import React, { useState, useEffect } from "react";
import Navbar from "../components/Navbar";
import TravelPermissionList from "../components/travel/TravelPermissionList";
import { travelService } from "../services/api/travel.service";
import { stateService } from "../services/api/state.service";
import { showErrorToast } from "../utils/showErrorToast";
import useUserDataStore from "../store/useUserDataStore";
import { FaMapMarkedAlt, FaLock, FaHistory, FaCrown } from "react-icons/fa";

const TravelPermissions = () => {
  const [activeTab, setActiveTab] = useState("incoming");
  const [isStateLeader, setIsStateLeader] = useState(false);
  const [ledState, setLedState] = useState(null);
  const [loading, setLoading] = useState(true);
  const { userData } = useUserDataStore();

  useEffect(() => {
    // Check if user is a state leader
    const checkStateLeadership = async () => {
      try {
        setLoading(true);
        // Get state led by the current user (if any)
        const state = await stateService.getStateLedByUser();

        if(state){
          setLedState(state);
  
          // If the user leads a state, they're a state leader
          setIsStateLeader(!!state);
          console.log("User is the leader of state:", state.name);

        } else {
          setIsStateLeader(false);
        }
      } catch (err) {
        console.error("Error checking state leadership:", err);
        showErrorToast("Failed to check state leadership status");
      } finally {
        setLoading(false);
      }
    };

    checkStateLeadership();
  }, []);

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />

      <div className="max-w-6xl mx-auto p-4 pt-20">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-white flex items-center">
            <FaMapMarkedAlt className="mr-3 text-blue-400" />
            Travel Permissions
          </h1>
          <p className="text-gray-400 mt-2">
            Manage travel permissions for your state or view your own requests.
          </p>

          {loading ? (
            <div className="mt-4 bg-gray-800 p-3 rounded-lg animate-pulse">
              <div className="h-5 bg-gray-700 rounded w-1/3"></div>
            </div>
          ) : isStateLeader && ledState ? (
            <div className="mt-4 bg-gray-800/50 p-3 rounded-lg">
              <div className="flex items-center text-yellow-400 mb-2">
                <FaCrown className="mr-2" /> You are the leader of:
              </div>
              <div className="flex flex-wrap gap-2">
                <span className="bg-gray-700 px-3 py-1 rounded-md text-white text-sm">
                  {ledState.name}
                </span>
              </div>
            </div>
          ) : null}
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700 mb-6">
          {isStateLeader && (
            <button
              className={`px-4 py-2 font-medium ${
                activeTab === "incoming"
                  ? "text-blue-400 border-b-2 border-blue-400"
                  : "text-gray-400 hover:text-gray-300"
              }`}
              onClick={() => setActiveTab("incoming")}
            >
              <FaLock className="inline mr-2" />
              Incoming Requests
            </button>
          )}

          <button
            className={`px-4 py-2 font-medium ${
              activeTab === "outgoing"
                ? "text-blue-400 border-b-2 border-blue-400"
                : "text-gray-400 hover:text-gray-300"
            }`}
            onClick={() => setActiveTab("outgoing")}
          >
            <FaHistory className="inline mr-2" />
            Your Requests
          </button>
        </div>

        {/* Content */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6">
          {activeTab === "incoming" && isStateLeader ? (
            <TravelPermissionList isStateLeader={true} ledState={ledState} />
          ) : (
            <TravelPermissionList isStateLeader={false} />
          )}
        </div>
      </div>
    </div>
  );
};

export default TravelPermissions;
