import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import useAuthStore from '../../store/useAuthStore';
import { toast } from 'react-toastify';

const WarSupplies = ({ warId, war }) => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [supplies, setSupplies] = useState(null);
  const [formData, setFormData] = useState({
    side: 'attacker',
    supplies: {
      ammunition: 0,
      food: 0,
      medical: 0,
      fuel: 0
    }
  });

  useEffect(() => {
    fetchSupplies();
  }, [warId]);

  // Determine if user is attacker or defender
  useEffect(() => {
    if (war && user) {
      // Check if user's state is the attacker
      const isAttacker = war.attackerState?.id === user.state?.id;
      // Check if user's state is the defender
      const isDefender = war.defenderState?.id === user.state?.id;

      if (isAttacker) {
        setFormData(prev => ({ ...prev, side: 'attacker' }));
      } else if (isDefender) {
        setFormData(prev => ({ ...prev, side: 'defender' }));
      }
    }
  }, [war, user]);

  const fetchSupplies = async () => {
    setLoading(true);
    try {
      const data = await warService.getWarSupplies(warId);
      setSupplies(data);
    } catch (error) {
      console.error('Failed to fetch war supplies:', error);
      toast.error('Failed to load war supplies');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === 'side') {
      setFormData(prev => ({ ...prev, side: value }));
    } else {
      setFormData(prev => ({
        ...prev,
        supplies: {
          ...prev.supplies,
          [name]: Math.max(0, parseInt(value) || 0)
        }
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await warService.addWarSupplies(warId, user.id, formData);
      toast.success('Supplies added successfully!');
      setSupplies(prev => {
        const updatedSupplies = { ...prev };
        if (formData.side === 'attacker') {
          updatedSupplies.attackerSupplies = result.updatedSupplies;
        } else {
          updatedSupplies.defenderSupplies = result.updatedSupplies;
        }
        return updatedSupplies;
      });

      // Reset form
      setFormData(prev => ({
        ...prev,
        supplies: {
          ammunition: 0,
          food: 0,
          medical: 0,
          fuel: 0
        }
      }));
    } catch (error) {
      console.error('Failed to add supplies:', error);
      toast.error(error.response?.data?.message || 'Failed to add supplies');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !supplies) {
    return <div className="text-center py-8"><div className="loader">Loading supplies...</div></div>;
  }

  return (
    <div className="space-y-6">
      {/* Current Supplies Status */}
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">War Supplies</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Attacker Supplies */}
          <div className="bg-gray-700 p-4 rounded-md">
            <h3 className="text-lg font-medium text-white mb-3">Attacker Supplies</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">Ammunition:</span>
                <span className="text-red-400">{supplies?.attackerSupplies.ammunition || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Food:</span>
                <span className="text-green-400">{supplies?.attackerSupplies.food || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Medical:</span>
                <span className="text-blue-400">{supplies?.attackerSupplies.medical || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Fuel:</span>
                <span className="text-yellow-400">{supplies?.attackerSupplies.fuel || 0}</span>
              </div>
              <div className="flex justify-between border-t border-gray-600 pt-2 mt-2">
                <span className="text-gray-400">Efficiency Bonus:</span>
                <span className="text-purple-400">+{supplies?.attackerSupplies.supplyEfficiencyBonus || 0}%</span>
              </div>
            </div>
          </div>

          {/* Defender Supplies */}
          <div className="bg-gray-700 p-4 rounded-md">
            <h3 className="text-lg font-medium text-white mb-3">Defender Supplies</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">Ammunition:</span>
                <span className="text-red-400">{supplies?.defenderSupplies.ammunition || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Food:</span>
                <span className="text-green-400">{supplies?.defenderSupplies.food || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Medical:</span>
                <span className="text-blue-400">{supplies?.defenderSupplies.medical || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Fuel:</span>
                <span className="text-yellow-400">{supplies?.defenderSupplies.fuel || 0}</span>
              </div>
              <div className="flex justify-between border-t border-gray-600 pt-2 mt-2">
                <span className="text-gray-400">Efficiency Bonus:</span>
                <span className="text-purple-400">+{supplies?.defenderSupplies.supplyEfficiencyBonus || 0}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Supplies Form */}
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">Add Supplies</h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="side" className="block text-sm font-medium text-gray-300 mb-1">
              Side
            </label>
            <select
              id="side"
              name="side"
              value={formData.side}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-white focus:outline-none focus:ring-neonBlue focus:border-neonBlue"
            >
              <option value="attacker">Attacker</option>
              <option value="defender">Defender</option>
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="ammunition" className="block text-sm font-medium text-gray-300 mb-1">
                Ammunition
              </label>
              <input
                type="number"
                id="ammunition"
                name="ammunition"
                min="0"
                value={formData.supplies.ammunition}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-white focus:outline-none focus:ring-neonBlue focus:border-neonBlue"
              />
            </div>

            <div>
              <label htmlFor="food" className="block text-sm font-medium text-gray-300 mb-1">
                Food
              </label>
              <input
                type="number"
                id="food"
                name="food"
                min="0"
                value={formData.supplies.food}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-white focus:outline-none focus:ring-neonBlue focus:border-neonBlue"
              />
            </div>

            <div>
              <label htmlFor="medical" className="block text-sm font-medium text-gray-300 mb-1">
                Medical
              </label>
              <input
                type="number"
                id="medical"
                name="medical"
                min="0"
                value={formData.supplies.medical}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-white focus:outline-none focus:ring-neonBlue focus:border-neonBlue"
              />
            </div>

            <div>
              <label htmlFor="fuel" className="block text-sm font-medium text-gray-300 mb-1">
                Fuel
              </label>
              <input
                type="number"
                id="fuel"
                name="fuel"
                min="0"
                value={formData.supplies.fuel}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-white focus:outline-none focus:ring-neonBlue focus:border-neonBlue"
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700 font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Processing...' : 'Add Supplies'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default WarSupplies;
