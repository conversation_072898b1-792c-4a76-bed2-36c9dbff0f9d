import { create } from "zustand";
import api from '../services/api/api';
import useAuthStore from './useAuthStore';

// Simplified caching: always return cached data unless explicitly forced.
const useUserDataStore = create((set, get) => ({
  userData: null,
  loading: false,
  error: null,

  fetchUserData: async (force = false) => {
    const { user } = useAuthStore.getState();
    if (!user?.id) return null;

    const state = get();

    // Return cached data if present and not forcing
    if (state.userData && !force) {
      return state.userData;
    }

    // If we don't have userData yet but have user, use it
    if (user && !force) {
      const userData = { ...user };
      set({ userData });
      return userData;
    }

    // If a request is already in progress (and not forcing), wait for it
    if (state.loading && !force) {
      return new Promise((resolve) => {
        const checkLoading = () => {
          const { loading, userData } = get();
          if (!loading) {
            resolve(userData);
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }

    // Otherwise, fetch fresh data
    set({ loading: true });
    try {
      const response = await api.get(`/users/${user.id}`);
      set({
        userData: response.data,
        loading: false,
        error: null
      });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch user data:', error);

      // If API call fails, at least use the basic user data we have
      if (user) {
        const userData = { ...user };
        set({
          userData,
          error: 'Failed to fetch detailed user data',
          loading: false
        });
        return userData;
      } else {
        set({ error: 'Failed to fetch user data', loading: false });
        return null;
      }
    }
  },

  updateUserData: (newData) => {
    set((state) => ({
      userData: state.userData ? {
        ...state.userData,
        ...newData
      } : newData
    }));
  },

  // Clear user data (useful for logout)
  clearUserData: () => {
    set({
      userData: null,
      loading: false,
      error: null
    });
  }
}));

export default useUserDataStore;
