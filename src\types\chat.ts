// Core Types
export interface User {
  id: number;
  username: string;
  level: number;
}

export interface Chat {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  content: string;
  type: 'text' | 'system';
  sender: User;
  readStatuses: ReadStatus[];
  isRead: boolean;
  createdAt: string;
}

export interface ReadStatus {
  userId: number;
  readAt: string;
}

// API Request/Response Types
export interface CreateChatDto {
  type: 'direct' | 'group';
  participantIds: number[];
  name?: string;
  description?: string;
}

export interface SendMessageDto {
  content: string;
  type: 'text' | 'system';
}

export interface PaginatedChatsResponse {
  chats: Chat[];
  hasMore: boolean;
  nextCursor?: string;
}

export interface PaginatedMessagesResponse {
  messages: Message[];
  hasMore: boolean;
  nextCursor?: string;
}

// WebSocket Event Types
export interface NewMessageEvent {
  chatId: string;
  message: Message;
}

export interface MessagesReadEvent {
  chatId: string;
  userId: number;
  markedCount: number;
}

export interface TypingEvent {
  chatId: string;
  userId: number;
  username: string;
  isTyping: boolean;
}

export interface ConnectedEvent {
  userId: number;
  timestamp: string;
}

export interface PongEvent {
  timestamp: string;
}

// WebSocket Outgoing Events
export interface SendMessageSocketDto {
  chatId: string;
  message: SendMessageDto;
}

export interface MarkReadSocketDto {
  chatId: string;
}

export interface TypingSocketDto {
  chatId: string;
}

// Chat Store State
export interface ChatState {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  
  // Chats data
  chats: Chat[];
  chatsLoading: boolean;
  chatsError: string | null;
  chatsHasMore: boolean;
  chatsCursor?: string;
  
  // Messages data
  messages: Record<string, Message[]>;
  messagesLoading: Record<string, boolean>;
  messagesError: Record<string, string | null>;
  messagesHasMore: Record<string, boolean>;
  messagesCursor: Record<string, string | undefined>;
  
  // UI state
  activeChatId: string | null;
  typingUsers: Record<string, TypingEvent[]>;
  
  // Unread counts
  totalUnreadCount: number;
}

// Chat Store Actions
export interface ChatActions {
  // Connection management
  connect: () => void;
  disconnect: () => void;
  
  // Chat management
  fetchChats: (cursor?: string) => Promise<void>;
  createChat: (data: CreateChatDto) => Promise<Chat>;
  setActiveChat: (chatId: string | null) => void;
  
  // Message management
  fetchMessages: (chatId: string, cursor?: string) => Promise<void>;
  sendMessage: (chatId: string, content: string) => Promise<void>;
  markAsRead: (chatId: string) => Promise<void>;
  
  // Typing indicators
  startTyping: (chatId: string) => void;
  stopTyping: (chatId: string) => void;
  
  // WebSocket event handlers
  handleNewMessage: (data: NewMessageEvent) => void;
  handleMessagesRead: (data: MessagesReadEvent) => void;
  handleTyping: (data: TypingEvent) => void;
  
  // Utility
  getChatById: (chatId: string) => Chat | undefined;
  getUnreadCount: () => number;
  reset: () => void;
}

// Component Props
export interface ChatInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface ChatListProps {
  chats: Chat[];
  activeChatId: string | null;
  onChatSelect: (chatId: string) => void;
  loading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

export interface MessageListProps {
  messages: Message[];
  loading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  currentUserId: number;
}

export interface MessageInputProps {
  onSendMessage: (content: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export interface CreateChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  onChatCreated: (chat: Chat) => void;
}

// Utility Types
export type ChatType = 'direct' | 'group';
export type MessageType = 'text' | 'system';
export type ConnectionStatus = 'connected' | 'connecting' | 'disconnected' | 'error';

// Error Types
export interface ChatError {
  code: string;
  message: string;
  details?: any;
}

// Configuration Types
export interface ChatConfig {
  maxMessageLength: number;
  maxChatNameLength: number;
  maxParticipants: number;
  typingTimeout: number;
  reconnectAttempts: number;
  reconnectDelay: number;
  heartbeatInterval: number;
  messagePageSize: number;
  chatPageSize: number;
}
