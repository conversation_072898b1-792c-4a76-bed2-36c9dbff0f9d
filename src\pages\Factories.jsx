import React, { useState, useEffect } from 'react';
import { factoryService } from '../services/api/factory.service';
import { toast } from 'react-toastify';

const Factories = () => {
  const [factories, setFactories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadFactories();
  }, []);

  const loadFactories = async () => {
    try {
      setLoading(true);
      const data = await factoryService.getAllFactories();
      setFactories(data);
    } catch (err) {
      setError('Failed to load factories');
      toast.error('Failed to load factories');
    } finally {
      setLoading(false);
    }
  };

  const handleWork = async (factoryId) => {
    try {
      await factoryService.workAtFactory(factoryId, { factoryId });
      toast.success('Successfully worked at factory!');
      loadFactories(); // Reload to get updated data
    } catch (err) {
      toast.error('Failed to work at factory');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Factories</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {factories.map((factory) => (
          <div
            key={factory.id}
            className="bg-gray-800 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow"
          >
            <h2 className="text-xl font-semibold mb-2">{factory.name}</h2>
            <div className="space-y-2 text-gray-300">
              <p>Type: {factory.type}</p>
              <p>Region: {factory.region.name}</p>
              <p>Owner: {factory.owner.username}</p>
              <p>Wage: ${factory.wage}</p>
              <p>Resource per work: {factory.resourcePerWork}</p>
              <p>Energy cost: {factory.energyCost}</p>
              <p>Workers: {factory.currentWorkers}/{factory.maxWorkers}</p>
            </div>
            <button
              onClick={() => handleWork(factory.id)}
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
              Work at Factory
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Factories; 