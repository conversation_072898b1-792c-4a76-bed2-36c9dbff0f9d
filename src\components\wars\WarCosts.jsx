import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { toast } from 'react-toastify';

const WarCosts = ({ warId }) => {
  const [loading, setLoading] = useState(true);
  const [costsData, setCostsData] = useState(null);

  useEffect(() => {
    fetchCostsData();
  }, [warId]);

  const fetchCostsData = async () => {
    setLoading(true);
    try {
      const data = await warService.getWarCosts(warId);
      setCostsData(data);
    } catch (error) {
      console.error('Failed to fetch war costs:', error);
      toast.error('Failed to load war costs data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="text-center py-8"><div className="loader">Loading costs data...</div></div>;
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">War Costs</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Attacker Costs */}
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Attacker Costs</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Gold Spent:</span>
              <span className="text-yellow-400">{costsData.attackerCosts.gold.toLocaleString()}</span>
            </div>
            <div className="border-t border-gray-600 pt-2">
              <h4 className="text-sm font-medium text-gray-300 mb-2">Resources Used</h4>
              <div className="grid grid-cols-1 gap-1">
                <div className="flex justify-between">
                  <span className="text-gray-400">Ammunition:</span>
                  <span className="text-red-400">{costsData.attackerCosts.resources.ammunition.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Fuel:</span>
                  <span className="text-blue-400">{costsData.attackerCosts.resources.fuel.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Food:</span>
                  <span className="text-green-400">{costsData.attackerCosts.resources.food.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Defender Costs */}
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">Defender Costs</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Gold Spent:</span>
              <span className="text-yellow-400">{costsData.defenderCosts.gold.toLocaleString()}</span>
            </div>
            <div className="border-t border-gray-600 pt-2">
              <h4 className="text-sm font-medium text-gray-300 mb-2">Resources Used</h4>
              <div className="grid grid-cols-1 gap-1">
                <div className="flex justify-between">
                  <span className="text-gray-400">Ammunition:</span>
                  <span className="text-red-400">{costsData.defenderCosts.resources.ammunition.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Fuel:</span>
                  <span className="text-blue-400">{costsData.defenderCosts.resources.fuel.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Food:</span>
                  <span className="text-green-400">{costsData.defenderCosts.resources.food.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Cost Comparison */}
      <div className="mt-6 bg-gray-700 p-4 rounded-md">
        <h3 className="text-md font-medium text-white mb-3">Cost Comparison</h3>
        <div className="space-y-4">
          {/* Gold Comparison */}
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm text-gray-400">Gold Expenditure</span>
              <div className="flex space-x-2">
                <span className="text-xs text-blue-400">Attacker</span>
                <span className="text-xs text-red-400">Defender</span>
              </div>
            </div>
            <div className="relative h-4 bg-gray-600 rounded-full overflow-hidden">
              {/* Calculate percentages */}
              {(() => {
                const totalGold = costsData.attackerCosts.gold + costsData.defenderCosts.gold;
                const attackerPercentage = totalGold > 0 ? (costsData.attackerCosts.gold / totalGold) * 100 : 50;
                
                return (
                  <div 
                    className="absolute top-0 left-0 h-full bg-blue-500"
                    style={{ width: `${attackerPercentage}%` }}
                  ></div>
                );
              })()}
            </div>
          </div>
          
          {/* Ammunition Comparison */}
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm text-gray-400">Ammunition Usage</span>
              <div className="flex space-x-2">
                <span className="text-xs text-blue-400">Attacker</span>
                <span className="text-xs text-red-400">Defender</span>
              </div>
            </div>
            <div className="relative h-4 bg-gray-600 rounded-full overflow-hidden">
              {(() => {
                const totalAmmo = costsData.attackerCosts.resources.ammunition + costsData.defenderCosts.resources.ammunition;
                const attackerPercentage = totalAmmo > 0 ? (costsData.attackerCosts.resources.ammunition / totalAmmo) * 100 : 50;
                
                return (
                  <div 
                    className="absolute top-0 left-0 h-full bg-blue-500"
                    style={{ width: `${attackerPercentage}%` }}
                  ></div>
                );
              })()}
            </div>
          </div>
          
          {/* Food Comparison */}
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm text-gray-400">Food Consumption</span>
              <div className="flex space-x-2">
                <span className="text-xs text-blue-400">Attacker</span>
                <span className="text-xs text-red-400">Defender</span>
              </div>
            </div>
            <div className="relative h-4 bg-gray-600 rounded-full overflow-hidden">
              {(() => {
                const totalFood = costsData.attackerCosts.resources.food + costsData.defenderCosts.resources.food;
                const attackerPercentage = totalFood > 0 ? (costsData.attackerCosts.resources.food / totalFood) * 100 : 50;
                
                return (
                  <div 
                    className="absolute top-0 left-0 h-full bg-blue-500"
                    style={{ width: `${attackerPercentage}%` }}
                  ></div>
                );
              })()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarCosts;
