/**
 * Test script to verify chat connection management
 * This script tests the fixes for multiple WebSocket connections
 */

import { chatWebSocketService } from '../services/chat/chatWebSocket.service.js';
import useChatStore from '../store/useChatStore.js';

/**
 * Test connection management
 */
export const testConnectionManagement = async () => {
  console.log('🧪 Testing Chat Connection Management...');
  
  const results = {
    singletonTest: false,
    connectionPreventionTest: false,
    cleanupTest: false,
    storeIntegrationTest: false
  };

  try {
    // Test 1: Singleton pattern
    console.log('📝 Test 1: Singleton Pattern');
    const service1 = chatWebSocketService;
    const service2 = chatWebSocketService;
    results.singletonTest = service1 === service2;
    console.log(`✅ Singleton test: ${results.singletonTest ? 'PASSED' : 'FAILED'}`);

    // Test 2: Connection prevention
    console.log('📝 Test 2: Multiple Connection Prevention');
    const mockToken = 'test-token-123';
    
    // Simulate multiple rapid connection attempts
    const connectionPromises = [];
    for (let i = 0; i < 5; i++) {
      connectionPromises.push(
        chatWebSocketService.connect(mockToken).catch(error => {
          // Expected to fail for some attempts
          return { error: error.message };
        })
      );
    }
    
    const connectionResults = await Promise.allSettled(connectionPromises);
    const successfulConnections = connectionResults.filter(result => 
      result.status === 'fulfilled' && !result.value?.error
    ).length;
    
    results.connectionPreventionTest = successfulConnections <= 1;
    console.log(`✅ Connection prevention test: ${results.connectionPreventionTest ? 'PASSED' : 'FAILED'}`);
    console.log(`   Successful connections: ${successfulConnections}/5 (should be ≤ 1)`);

    // Test 3: Cleanup
    console.log('📝 Test 3: Connection Cleanup');
    const statusBefore = chatWebSocketService.getStatus();
    chatWebSocketService.forceDisconnect();
    const statusAfter = chatWebSocketService.getStatus();
    
    results.cleanupTest = !statusAfter.isConnected && !statusAfter.isConnecting;
    console.log(`✅ Cleanup test: ${results.cleanupTest ? 'PASSED' : 'FAILED'}`);
    console.log(`   Before: connected=${statusBefore.isConnected}, connecting=${statusBefore.isConnecting}`);
    console.log(`   After: connected=${statusAfter.isConnected}, connecting=${statusAfter.isConnecting}`);

    // Test 4: Store integration
    console.log('📝 Test 4: Store Integration');
    const { connect, disconnect, reset } = useChatStore.getState();
    
    // Test store reset
    reset();
    const storeState = useChatStore.getState();
    results.storeIntegrationTest = !storeState.isConnected && !storeState.isConnecting;
    console.log(`✅ Store integration test: ${results.storeIntegrationTest ? 'PASSED' : 'FAILED'}`);

    // Summary
    console.log('\n📊 Test Results Summary:');
    const allPassed = Object.values(results).every(result => result);
    console.log(`Overall: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    Object.entries(results).forEach(([test, passed]) => {
      console.log(`  ${test}: ${passed ? '✅' : '❌'}`);
    });

    return { success: allPassed, results };

  } catch (error) {
    console.error('❌ Test execution failed:', error);
    return { success: false, error: error.message, results };
  }
};

/**
 * Test connection state synchronization
 */
export const testConnectionStateSynchronization = () => {
  console.log('🧪 Testing Connection State Synchronization...');
  
  const chatStore = useChatStore.getState();
  const serviceStatus = chatWebSocketService.getStatus();
  
  const isInSync = chatStore.isConnected === serviceStatus.isConnected &&
                   chatStore.isConnecting === serviceStatus.isConnecting;
  
  console.log('📊 State Synchronization:');
  console.log(`  Store: connected=${chatStore.isConnected}, connecting=${chatStore.isConnecting}`);
  console.log(`  Service: connected=${serviceStatus.isConnected}, connecting=${serviceStatus.isConnecting}`);
  console.log(`  In Sync: ${isInSync ? '✅' : '❌'}`);
  
  return isInSync;
};

/**
 * Run all tests
 */
export const runAllChatTests = async () => {
  console.log('🚀 Starting Chat Connection Tests...\n');
  
  const connectionTest = await testConnectionManagement();
  const syncTest = testConnectionStateSynchronization();
  
  console.log('\n🏁 All Tests Complete!');
  console.log(`Connection Management: ${connectionTest.success ? '✅' : '❌'}`);
  console.log(`State Synchronization: ${syncTest ? '✅' : '❌'}`);
  
  return {
    connectionManagement: connectionTest,
    stateSynchronization: syncTest,
    overallSuccess: connectionTest.success && syncTest
  };
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.chatConnectionTests = {
    testConnectionManagement,
    testConnectionStateSynchronization,
    runAllChatTests
  };
}
