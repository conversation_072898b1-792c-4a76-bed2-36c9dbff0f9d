import { useEffect } from 'react';
import useResourcesStore from '../store/useResourcesStore';
import useUserDataStore from '../store/useUserDataStore';
import useAuthStore from '../store/useAuthStore';

export function useUserResources() {
  const { user } = useAuthStore();
  const { money, gold, loading, fetchResources } = useResourcesStore();
  const { fetchUserData } = useUserDataStore();

  useEffect(() => {
    if (!user?.id) return;

    // Use the cached data if available, or fetch new data if needed
    fetchUserData(false).then(() => {
      // Then update the resources store with that data
      fetchResources();
    });
    // Only run this effect when the user ID changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id]);

  // The refetch function now first updates user data, then resources
  // Force refresh when explicitly called
  const refetchAll = async () => {
    await fetchUserData(true); // Force refresh
    fetchResources();
  };

  return { money, gold, loading, refetch: refetchAll };
}