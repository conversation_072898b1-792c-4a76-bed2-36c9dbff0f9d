import { useState } from "react";
import api from "../services/api/api";
import { toast } from "react-toastify";

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [submitted, setSubmitted] = useState(false);

  const handleForgotPassword = async (e) => {
    e.preventDefault();
    try {
      await api.post("/auth/forgot-password", { email });
      setSubmitted(true);
      toast.success("If an account exists, you will receive an email shortly.");
    } catch (error) {
      toast.error("Something went wrong. Please try again.");
    }
  };

  return (
    <div className="h-screen flex items-center justify-center">
      <form className="bg-darkCard p-6 rounded-lg shadow-lg" onSubmit={handleForgotPassword}>
        <h2 className="text-2xl text-neonBlue font-bold mb-4">Forgot Password</h2>

        {submitted ? (
          <><p className="text-green-400">
                      If an account exists, you will receive an email shortly.
                  </p><p className="mt-2 text-sm text-gray-400">
                          <a href="/login" className="text-neonBlue">
                              Login
                          </a>
                      </p></>
        ) : (
          <>
            <input
              type="email"
              className="w-full p-2 mb-3 rounded bg-gray-800 text-white"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <button type="submit" className="w-full bg-neonGreen p-2 rounded">
              Reset Password
            </button>
          </>
        )}
      </form>
    </div>
  );
}
